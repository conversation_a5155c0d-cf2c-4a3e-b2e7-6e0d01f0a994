import { useState, useEffect } from 'react'
import { ImageGrid } from '@/components/ImageGrid.tsx'
import { SearchControlPanel } from '@/components/SearchControlPanel.tsx'
import { SearchResultsHeader } from '@/components/SearchResultsHeader.tsx'
import { LocationTimeSearch } from '@/components/LocationTimeSearch.tsx'
import {
  Search,
  Sparkles,
  MapPin,
  Loader2,
  AlertCircle,
  TrendingUp,
  Clock,
  Tag
} from 'lucide-react'
import {
  type SearchMethod,
  type EnhancedGalleryQueryResult,
  DEFAULT_SEARCH_PREFERENCES,
  ERROR_MESSAGES
} from '@/types/search'
import { trpcClient } from '../lib/trpcClient'

interface SearchPageProps {
}

export function SearchPage(): JSX.Element {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<EnhancedGalleryQueryResult>({
    images: [],
    total: 0,
    loading: false
  })
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [popularTags, setPopularTags] = useState<string[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [hasSearched, setHasSearched] = useState(false) // 新增：跟踪是否已执行过搜索
  const [activeTab, setActiveTab] = useState<'semantic' | 'location-time'>('semantic')
  
  // 新的搜索控制状态
  const [selectedMethod, setSelectedMethod] = useState<SearchMethod>(DEFAULT_SEARCH_PREFERENCES.method)
  const [similarityThreshold, setSimilarityThreshold] = useState<number>(DEFAULT_SEARCH_PREFERENCES.threshold)
  const [showThresholdWarning, setShowThresholdWarning] = useState(false)

  // 加载搜索历史和热门标签
  useEffect(() => {
    loadInitialData()
    loadSearchPreferences()
  }, [])

  // 加载搜索偏好设置
  const loadSearchPreferences = () => {
    try {
      const savedPreferences = localStorage.getItem('searchPreferences')
      if (savedPreferences) {
        const preferences = JSON.parse(savedPreferences)
        setSelectedMethod(preferences.method || DEFAULT_SEARCH_PREFERENCES.method)
        setSimilarityThreshold(preferences.threshold || DEFAULT_SEARCH_PREFERENCES.threshold)
      }
    } catch (error) {
      console.error('加载搜索偏好失败:', error)
    }
  }

  // 保存搜索偏好设置
  const saveSearchPreferences = (method: SearchMethod, threshold: number) => {
    try {
      const preferences = {
        method,
        threshold,
        lastUpdated: new Date().toISOString()
      }
      localStorage.setItem('searchPreferences', JSON.stringify(preferences))
    } catch (error) {
      console.error('保存搜索偏好失败:', error)
    }
  }

  const loadInitialData = async () => {
    try {
      // 从本地存储加载搜索历史
      const savedHistory = localStorage.getItem('searchHistory')
      if (savedHistory) {
        setSearchHistory(JSON.parse(savedHistory))
      }

      // 获取热门标签
      const result = await trpcClient.search.getPopularTags.query({ limit: 10 })
      if (result.success) {
        setPopularTags(result.tags)
      } else {
        console.error('获取热门标签失败:', result.success)
      }
    } catch (error) {
      console.error('加载初始数据失败:', error)
    }
  }

  // 执行搜索
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults({
        images: [],
        total: 0,
        loading: false
      })
      setHasSearched(false) // 重置搜索状态
      return
    }

    setIsSearching(true)
    setHasSearched(true) // 标记已执行搜索
    setSearchResults(prev => ({ ...prev, loading: true }))

    try {
      // 使用trpc搜索接口
      const result = await trpcClient.search.searchImages.query({
        query: query.trim(),
        method: selectedMethod,
        threshold: selectedMethod === 'vector' ? similarityThreshold : undefined,
        limit: 50
      })

      console.log('🔍 [SearchPage] 搜索结果:', result)
      console.log('📊 [SearchPage] 图片数量:', result.images.length)
      console.log('🖼️ [SearchPage] 图片信息:', result.images.map(img => ({
        id: img.id,
        title: img.title,
        url: img.url,
        similarity: img.similarity
      })))

      if (result.success) {
        // 适配数据格式
        const adaptedImages = result.images.map(img => {
          // 确保tags是字符串数组
          let tags: string[] = []
          if (Array.isArray(img.tags)) {
            tags = img.tags
          } else if (typeof img.tags === 'string') {
            try {
              tags = JSON.parse(img.tags)
            } catch (e) {
              tags = []
            }
          }

          return {
            ...img,
            tags: tags,
            description: img.description || '',
            location: img.location || '',
            camera: img.camera || '',
            colors: img.colors || [],
            fileSize: img.fileSize || '',
            resolution: img.resolution || '',
            exif: img.exif || {
              iso: 0,
              aperture: '',
              shutterSpeed: '',
              focalLength: ''
            }
          }
        })

        setSearchResults({
          images: adaptedImages,
          total: result.total,
          loading: false,
          // metadata: result.metadata
        })

        // 添加到搜索历史
        const newHistory = [query, ...searchHistory.filter((h: any) => h !== query)].slice(0, 10)
        setSearchHistory(newHistory)
        localStorage.setItem('searchHistory', JSON.stringify(newHistory))
      } else {
        setSearchResults({
          images: [],
          total: 0,
          loading: false,
          // error: result.success
        })
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      let methodSpecificError = errorMessage
      
      // 提供方法特定的错误信息
      if (selectedMethod === 'hybrid') {
        methodSpecificError = `智能混合搜索失败: ${errorMessage}`
      } else if (selectedMethod === 'vector') {
        methodSpecificError = `语义向量搜索失败: ${errorMessage}`
      } else {
        methodSpecificError = `智能搜索失败: ${errorMessage}`
      }
      
      setSearchResults({
        images: [],
        total: 0,
        loading: false,
        error: methodSpecificError
      })
    } finally {
      setIsSearching(false)
    }
  }

  // 处理搜索输入变化
  const handleSearchInput = (value: string) => {
    setSearchQuery(value)
  }

  // 处理搜索按钮点击
  const handleSearchButtonClick = () => {
    handleSearch(searchQuery)
  }

  // 处理回车键搜索
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearchButtonClick()
    }
  }

  // 快速搜索（点击历史记录或标签）
  const quickSearch = (query: string) => {
    setSearchQuery(query)
    handleSearch(query)
  }

  // 清除搜索历史
  const clearSearchHistory = () => {
    setSearchHistory([])
    localStorage.removeItem('searchHistory')
  }

  return (
      <div className="p-8 space-y-8">
        {/* 页面标题 */}
        <div className="space-y-2">
          <h1 className="text-3xl font-semibold text-slate-900 dark:text-slate-100">智能搜索</h1>
          <p className="text-slate-600 dark:text-slate-400">
            使用 AI 语义理解或位置时间信息，找到你想要的图片
          </p>
        </div>

      {/* 选项卡导航 */}
      <div className="flex space-x-1 bg-slate-100/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-xl p-1">
        <button
          onClick={() => setActiveTab('semantic')}
          className={`flex items-center px-4 py-2 rounded-lg font-medium transition-all ${
            activeTab === 'semantic'
              ? 'bg-white dark:bg-slate-700 text-[#007aff] shadow-sm'
              : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200'
          }`}
        >
          <Sparkles className="h-4 w-4 mr-2" />
          语义搜索
        </button>
        <button
          onClick={() => setActiveTab('location-time')}
          className={`flex items-center px-4 py-2 rounded-lg font-medium transition-all ${
            activeTab === 'location-time'
              ? 'bg-white dark:bg-slate-700 text-[#007aff] shadow-sm'
              : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200'
          }`}
        >
          <MapPin className="h-4 w-4 mr-2" />
          位置时间
        </button>
      </div>

      {/* 语义搜索内容 */}
      {activeTab === 'semantic' && (
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 bg-[#007aff]/10 rounded-lg">
              <Search className="h-5 w-5 text-[#007aff]" />
            </div>
            <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">语义搜索</h2>
          </div>

          <div className="space-y-6">
            {/* 搜索输入框 */}
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => handleSearchInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="描述你想找的图片..."
                className="w-full px-4 py-3 pr-12 bg-white/80 dark:bg-slate-700/80 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl text-slate-900 dark:text-slate-100 placeholder-slate-500 dark:placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-[#007aff]/50 focus:border-[#007aff]/50 transition-all"
              />
              <button
                onClick={handleSearchButtonClick}
                disabled={isSearching || !searchQuery.trim()}
                className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-[#007aff] hover:bg-[#0056cc] disabled:bg-slate-300 dark:disabled:bg-slate-600 text-white rounded-lg transition-colors disabled:cursor-not-allowed"
              >
                {isSearching ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
              </button>
            </div>

            {/* 搜索控制 */}
            <SearchControlPanel
              selectedMethod={selectedMethod}
              onMethodChange={(method) => {
                setSelectedMethod(method)
                saveSearchPreferences(method, similarityThreshold)
              }}
              threshold={similarityThreshold}
              onThresholdChange={(threshold) => {
                setSimilarityThreshold(threshold)
                saveSearchPreferences(selectedMethod, threshold)
                
                // 显示阈值警告
                if (threshold < 0.3 || threshold > 0.9) {
                  setShowThresholdWarning(true)
                  setTimeout(() => setShowThresholdWarning(false), 3000)
                }
              }}
            />

            {/* 阈值警告 */}
            {showThresholdWarning && (
              <div className="p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400 mr-2" />
                  <p className="text-sm text-amber-700 dark:text-amber-300">
                    {similarityThreshold < 0.3 
                      ? '阈值过低可能返回不相关的结果' 
                      : '阈值过高可能找不到足够的结果'
                    }
                  </p>
                </div>
              </div>
            )}

            {/* 搜索建议 */}
            {!searchQuery && (
              <div className="space-y-4">
                {/* 搜索历史 */}
                {searchHistory.length > 0 && (
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        搜索历史
                      </h3>
                      <button
                        onClick={clearSearchHistory}
                        className="text-xs text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors"
                      >
                        清除
                      </button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {searchHistory.map((query, index) => (
                        <button
                          key={index}
                          onClick={() => quickSearch(query)}
                          className="px-3 py-1.5 text-sm bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 hover:bg-[#007aff]/10 hover:text-[#007aff] rounded-lg transition-all"
                        >
                          {query}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* 热门标签 */}
                {popularTags.length > 0 && (
                  <div>
                    <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3 flex items-center">
                      <Tag className="h-4 w-4 mr-1" />
                      热门标签
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {popularTags.map((tag) => (
                        <button
                          key={tag}
                          onClick={() => quickSearch(tag)}
                          className="px-3 py-1.5 text-sm border border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400 hover:bg-[#007aff]/10 hover:text-[#007aff] hover:border-[#007aff]/50 rounded-lg transition-all"
                        >
                          {tag}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* 搜索提示 */}
                <div className="p-4 bg-slate-50/60 dark:bg-slate-700/60 backdrop-blur-sm rounded-xl">
                  <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">搜索提示</h3>
                  <ul className="text-xs text-slate-600 dark:text-slate-400 space-y-1">
                    <li>• 描述图片内容：如"蓝天白云"、"猫咪"、"夕阳"</li>
                    <li>• 描述场景：如"海边"、"森林"、"城市夜景"</li>
                    <li>• 描述情感：如"温馨"、"浪漫"、"宁静"</li>
                    <li>• 使用组合词：如"可爱的小狗在公园里玩耍"</li>
                  </ul>
                </div>
              </div>
            )}
          </div>

          {/* 搜索结果 */}
          {(searchResults.loading || hasSearched || searchResults.error) && (
            <div className="mt-8 space-y-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-[#007aff]/10 rounded-lg">
                  <Sparkles className="h-5 w-5 text-[#007aff]" />
                </div>
                <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">搜索结果</h2>
                {hasSearched && !searchResults.loading && (
                  <span className="px-3 py-1 text-sm bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 rounded-lg">
                    {searchResults.total} 张图片
                  </span>
                )}
              </div>

              {searchResults.loading ? (
                <div className="flex items-center justify-center py-16">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#007aff]" />
                    <p className="text-slate-600 dark:text-slate-400">
                      {selectedMethod === 'auto' && '正在智能搜索相关图片...'}
                      {selectedMethod === 'hybrid' && '正在使用智能混合搜索...'}
                      {selectedMethod === 'vector' && '正在使用语义向量搜索...'}
                    </p>
                  </div>
                </div>
              ) : searchResults.error ? (
                <div className="flex items-center justify-center py-16">
                  <div className="text-center max-w-lg mx-auto">
                    <AlertCircle className="h-8 w-8 mx-auto mb-4 text-red-500" />
                    <h3 className="font-medium mb-2 text-slate-900 dark:text-slate-100">搜索失败</h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                      {searchResults.error}
                    </p>
                    <div className="flex flex-col sm:flex-row gap-2 justify-center">
                      <button
                        onClick={() => handleSearch(searchQuery)}
                        className="px-4 py-2 bg-[#007aff] hover:bg-[#0056cc] text-white rounded-lg transition-colors"
                      >
                        重试搜索
                      </button>
                      {selectedMethod !== 'auto' && (
                        <button
                          onClick={() => {
                            setSelectedMethod('auto')
                            saveSearchPreferences('auto', similarityThreshold)
                            handleSearch(searchQuery)
                          }}
                          className="px-4 py-2 bg-white/60 dark:bg-slate-700/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-lg text-slate-600 dark:text-slate-400 hover:bg-white/80 dark:hover:bg-slate-700/80 transition-colors"
                        >
                          尝试自动选择
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ) : searchResults.images.length === 0 ? (
                <div className="space-y-6">
                  {/* 搜索结果头部 - 显示搜索元数据 */}
                  {searchResults.metadata && (
                    <SearchResultsHeader
                      metadata={searchResults.metadata}
                      totalResults={searchResults.total}
                      query={searchQuery}
                    />
                  )}
                  
                  <div className="text-center py-16">
                    <div className="p-4 bg-slate-100/60 dark:bg-slate-700/60 backdrop-blur-sm rounded-2xl w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                      <Search className="h-10 w-10 text-slate-400" />
                    </div>
                    <h3 className="text-xl font-semibold mb-3 text-slate-900 dark:text-slate-100">未找到相关图片</h3>
                    <p className="text-slate-600 dark:text-slate-400 mb-6">
                      尝试使用不同的关键词或更通用的描述
                    </p>
                    <div className="flex flex-wrap gap-2 justify-center">
                      {popularTags.slice(0, 5).map((tag) => (
                        <button
                          key={tag}
                          onClick={() => quickSearch(tag)}
                          className="px-3 py-1.5 text-sm border border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400 hover:bg-[#007aff]/10 hover:text-[#007aff] hover:border-[#007aff]/50 rounded-lg transition-all"
                        >
                          试试: {tag}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* 搜索结果头部 - 显示搜索元数据 */}
                  {searchResults.metadata && (
                    <SearchResultsHeader
                      metadata={searchResults.metadata}
                      totalResults={searchResults.total}
                      query={searchQuery}
                    />
                  )}
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 text-sm text-slate-600 dark:text-slate-400">
                      <TrendingUp className="h-4 w-4" />
                      <span>按相似度排序</span>
                    </div>
                  </div>
                  <ImageGrid 
                    images={searchResults.images}
                    onImageSelect={() => {}}
                  />
                </div>
              )}
            </div>
          )}
        </div>
      )}

        {/* 位置时间搜索内容 */}
        {activeTab === 'location-time' && (
          <LocationTimeSearch onImageSelect={() => {}} />
        )}
      </div>
  )
}