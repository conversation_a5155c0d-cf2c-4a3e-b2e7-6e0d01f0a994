// import { databaseService } from './database'
// import { ImageRecord } from '../types/database'
// import { ImageData } from '../data/mockData'
// import {
//   SearchMethod,
//   SearchMetadata,
//   EnhancedGalleryQueryResult,
//   EnhancedImageData,
//   SearchError,
//   createSearchError,
//   getErrorTypeFromError,
//   validateThreshold
// } from '../types/search'
// import { trpcClient } from './trpcClient'
//
// /**
//  * 搜索选项
//  */
// export interface SearchOptions {
//   similarityThreshold: number
//   enableSemanticFallback: boolean
// }
//
// /**
//  * 排序选项
//  */
// export interface SortOption {
//   key: keyof ImageData
//   label: string
//   direction: 'asc' | 'desc'
//   value: string
// }
//
// /**
//  * 筛选选项
//  */
// export interface FilterOptions {
//   tags?: string[]
//   dateRange?: {
//     start?: string
//     end?: string
//   }
//   searchQuery?: string
// }
//
// /**
//  * 图片库查询结果
//  */
// export interface GalleryQueryResult {
//   images: ImageData[];
//   total: number;
//   loading: boolean;
//   error?: string;
//   metadata?: {
//     totalMatches: number;
//     originalKeywords: string[];
//     expandedKeywords: string[];
//   };
// }
//
// /**
//  * 预定义的排序选项
//  */
// export const SORT_OPTIONS: SortOption[] = [
//   { key: 'uploadTime', label: '上传时间（最新）', direction: 'desc', value: 'uploadTime_desc' },
//   { key: 'uploadTime', label: '上传时间（最旧）', direction: 'asc', value: 'uploadTime_asc' },
//   { key: 'title', label: '名称（A-Z）', direction: 'asc', value: 'title_asc' },
//   { key: 'title', label: '名称（Z-A）', direction: 'desc', value: 'title_desc' },
//   { key: 'fileSize', label: '文件大小（大到小）', direction: 'desc', value: 'fileSize_desc' },
//   { key: 'fileSize', label: '文件大小（小到大）', direction: 'asc', value: 'fileSize_asc' }
// ]
//
// /**
//  * 图片库服务类
//  */
// export class GalleryService {
//   private static instance: GalleryService | null = null
//   private tagsCache: string[] | null = null
//   private tagsCacheTime: number = 0
//   private readonly CACHE_DURATION = 30000 // 30秒缓存
//
//   private constructor() {
//     // 私有构造函数，确保单例
//   }
//
//   /**
//    * 获取图片库服务实例
//    */
//   static getInstance(): GalleryService {
//     if (!this.instance) {
//       this.instance = new GalleryService()
//     }
//     return this.instance
//   }
//
//
//
//   /**
//    * 通用图片查询方法，支持搜索、标签筛选和排序
//    */
//   async getImages(params: {
//     query?: string
//     tags?: string[]
//     sort?: string
//     page?: number
//     limit?: number
//     skipInit?: boolean  // 新增：跳过初始化检查
//   }): Promise<GalleryQueryResult> {
//     try {
//       console.log('Gallery Service: getImages 调用参数:', params)
//
//       const { query, tags, sort, page = 1, limit = 50, skipInit = false } = params
//
//       // 如果有搜索查询，使用智能搜索
//       if (query && query.trim()) {
//         console.log(`Gallery Service: 执行搜索查询: "${query}"`)
//         return await this.searchImages(query.trim(), limit)
//       }
//
//       // 如果有标签筛选，使用标签查询
//       if (tags && tags.length > 0) {
//         console.log(`Gallery Service: 执行标签查询:`, tags)
//         return await this.getImagesByTags(tags, limit)
//       }
//
//       // 否则获取所有图片
//       console.log(`Gallery Service: 获取所有图片，限制: ${limit}`)
//       const result = await this.getAllImages(limit)
//
//       // 应用排序
//       if (sort && result.images.length > 0) {
//         const sortOption = SORT_OPTIONS.find((opt) => opt.label === sort) || SORT_OPTIONS[0]
//         console.log(`Gallery Service: 应用排序: ${sortOption.label}`)
//         result.images = this.sortImages(result.images, sortOption)
//       }
//
//       return result
//
//     } catch (error) {
//       console.error('Gallery Service: getImages 失败:', error)
//       return {
//         images: [],
//         total: 0,
//         loading: false,
//         error: error instanceof Error ? error.message : String(error)
//       }
//     }
//   }
//
//   /**
//    * 查询所有图片（使用 tRPC client）
//    */
//   async getAllImages(limit: number = 100): Promise<GalleryQueryResult> {
//     try {
//       console.log(`Gallery Service: 开始查询所有图片，限制数量: ${limit}`);
//
//       const result = await trpcClient.image.queryImages.query({ limit })
//      
//       console.log('Gallery Service: tRPC 查询结果:', {
//         success: !result.error,
//         total: result.total || 0,
//         imagesCount: result.images?.length || 0,
//         error: result.error
//       });
//      
//       if (result.error || !result.images) {
//         return {
//           images: [],
//           total: 0,
//           loading: false,
//           error: result.error || 'No results returned'
//         }
//       }
//
//       const images = result.images.map((record) => this.convertImageRecordToImageData(record))
//      
//       console.log(`Gallery Service: 转换完成，得到 ${images.length} 张图片`);
//      
//       return {
//         images: this.sortImages(images, SORT_OPTIONS[0]), // 默认按上传时间排序
//         total: result.total || 0,
//         loading: false
//       }
//     } catch (error) {
//       console.error('Gallery Service: 查询图片失败:', error)
//       return {
//         images: [],
//         total: 0,
//         loading: false,
//         error: error instanceof Error ? error.message : String(error)
//       }
//     }
//   }
//
//   /**
//    * 根据标签筛选图片（使用 tRPC client）
//    */
//   async getImagesByTags(tags: string[], limit: number = 100): Promise<GalleryQueryResult> {
//     try {
//       const result = await trpcClient.image.queryImages.query({ tags, limit })
//       if (result.error) {
//         return {
//           images: [],
//           total: 0,
//           loading: false,
//           error: result.error
//         }
//       }
//
//       const images = result.images.map((record) => this.convertImageRecordToImageData(record))
//       return {
//         images,
//         total: result.total,
//         loading: false
//       }
//     } catch (error) {
//       console.error('按标签查询图片失败:', error)
//       return {
//         images: [],
//         total: 0,
//         loading: false,
//         error: error instanceof Error ? error.message : String(error)
//       }
//     }
//   }
//
//   /**
//    * 根据指定方法搜索图片（新的主要搜索入口，带增强错误处理）
//    */
//   async searchImagesWithMethod(
//     query: string,
//     method: SearchMethod,
//     threshold?: number,
//     limit: number = 50
//   ): Promise<EnhancedGalleryQueryResult> {
//     const startTime = Date.now();
//
//     // 验证阈值（如果提供）
//     if (threshold !== undefined) {
//       const validation = validateThreshold(threshold);
//       if (!validation.isValid) {
//         const searchError = createSearchError('INVALID_THRESHOLD', method, undefined, validation.warning);
//         return this.createErrorResult(searchError, startTime, method, threshold);
//       }
//     }
//
//     try {
//       console.log(`Gallery Service: 使用方法 "${method}" 搜索 - 查询: "${query}"`);
//       console.log(`🔍 [GalleryService] 搜索参数:`, { query, method, threshold, limit });
//
//       let result: GalleryQueryResult;
//       let actualMethod: 'hybrid' | 'vector';
//       let fallbackOccurred = false;
//       let searchError: SearchError | null = null;
//
//       switch (method) {
//         case 'auto':
//           // 自动选择：先尝试混合搜索，失败时回退到向量搜索
//           try {
//             result = await this.hybridSearchOnlyWithRetry(query, limit);
//             actualMethod = 'hybrid';
//           } catch (error) {
//             console.warn('智能混合搜索失败，回退到语义向量搜索:', error);
//             searchError = this.createSearchErrorFromException(error, 'hybrid');
//
//             try {
//               result = await this.vectorSearchWithThresholdAndRetry(query, threshold || 0.6, limit);
//               actualMethod = 'vector';
//               fallbackOccurred = true;
//             } catch (fallbackError) {
//               // 如果回退也失败，抛出原始错误和回退错误的组合
//               const combinedError = this.createFallbackError(searchError, fallbackError, method);
//               return this.createErrorResult(combinedError, startTime, method, threshold);
//             }
//           }
//           break;
//
//         case 'hybrid':
//           try {
//             result = await this.hybridSearchOnlyWithRetry(query, limit);
//             actualMethod = 'hybrid';
//           } catch (error) {
//             const hybridError = this.createSearchErrorFromException(error, 'hybrid');
//             return this.createErrorResult(hybridError, startTime, method, threshold);
//           }
//           break;
//
//         case 'vector':
//           try {
//             result = await this.vectorSearchWithThresholdAndRetry(query, threshold || 0.6, limit);
//             actualMethod = 'vector';
//           } catch (error) {
//             const vectorError = this.createSearchErrorFromException(error, 'vector');
//             return this.createErrorResult(vectorError, startTime, method, threshold);
//           }
//           break;
//
//         default: {
//           const unknownError = createSearchError('UNKNOWN_ERROR', method, undefined, `不支持的搜索方法: ${method}`);
//           return this.createErrorResult(unknownError, startTime, method, threshold);
//         }
//       }
//
//       const duration = Date.now() - startTime;
//
//       // 计算搜索统计信息
//       const averageSimilarity = result.images.length > 0
//         ? result.images.reduce((sum, img) => sum + (img.similarity || 0), 0) / result.images.length
//         : undefined;
//
//       // 创建搜索元数据
//       const metadata: SearchMetadata = {
//         method,
//         actualMethod,
//         fallbackOccurred,
//         duration,
//         threshold,
//         totalResults: result.total,
//         averageSimilarity,
//         timestamp: new Date().toISOString()
//       };
//
//       // 转换为增强结果格式
//       const enhancedImages: EnhancedImageData[] = result.images.map((img) => ({
//         ...img,
//         searchMetadata: {
//           matchType: actualMethod,
//           similarity: img.similarity,
//           relevanceReason: this.generateRelevanceReason(img, query, actualMethod)
//         }
//       }));
//
//       console.log(`🎯 [GalleryService] 搜索完成 - 方法: ${actualMethod}, 耗时: ${duration}ms, 结果: ${result.images.length}`);
//       console.log(`📋 [GalleryService] 增强结果详情:`, enhancedImages.map(img => ({
//         id: img.id,
//         title: img.title,
//         url: img.url,
//         similarity: img.similarity
//       })));
//
//       return {
//         images: enhancedImages,
//         total: result.total,
//         loading: false,
//         error: result.error,
//         metadata
//       };
//
//     } catch (error) {
//       console.error('Gallery Service: 搜索失败:', error);
//       const searchError = this.createSearchErrorFromException(error, method);
//       return this.createErrorResult(searchError, startTime, method, threshold);
//     }
//   }
//
//   /**
//    * 直接执行混合搜索（不回退）
//    */
//   async hybridSearchOnly(query: string, limit: number = 50): Promise<GalleryQueryResult> {
//     try {
//       console.log(`🔀 [HybridSearch] 开始执行智能混合搜索 - 查询: "${query}"`);
//       console.log(`📊 [HybridSearch] 搜索参数:`, {
//         query: query,
//         limit: limit,
//         enableKeywordExpansion: true,
//         similarityThreshold: 0.7
//       });
//
//       // 首先检查数据库连接状态
//       console.log(`🔗 [HybridSearch] 检查数据库连接状态...`);
//       try {
//         const connectionTest = await databaseService.manualTestConnection();
//         console.log(`📊 [HybridSearch] 数据库连接测试结果:`, connectionTest);
//        
//         if (!connectionTest.success) {
//           throw new Error(`数据库连接失败: ${connectionTest.error}`);
//         }
//       } catch (connectionError) {
//         console.error(`❌ [HybridSearch] 数据库连接检查失败:`, connectionError);
//         throw new Error(`数据库连接失败: ${connectionError}`);
//       }
//
//       // 检查数据库中是否有图片数据
//       console.log(`🔍 [HybridSearch] 检查数据库图片数量...`);
//       try {
//         const allImagesResult = await databaseService.queryImages({ limit: 1 });
//         console.log(`📈 [HybridSearch] 数据库图片检查:`, {
//           hasResults: !!allImagesResult.results,
//           totalImages: allImagesResult.total,
//           hasError: !!allImagesResult.error,
//           error: allImagesResult.error
//         });
//        
//         if (allImagesResult.error) {
//           throw new Error(`数据库查询失败: ${allImagesResult.error}`);
//         }
//        
//         if (!allImagesResult.results || allImagesResult.total === 0) {
//           console.warn(`⚠️ [HybridSearch] 数据库中没有图片数据`);
//           return {
//             images: [],
//             total: 0,
//             loading: false
//           };
//         }
//       } catch (queryError) {
//         console.error(`❌ [HybridSearch] 数据库图片查询失败:`, queryError);
//         throw new Error(`数据库查询失败: ${queryError}`);
//       }
//
//       // 执行智能混合搜索
//       console.log(`📡 [HybridSearch] 调用 databaseService.enhancedHybridSearch...`);
//       const result = await databaseService.enhancedHybridSearch(
//         query,
//         limit,
//         true, // 启用关键词扩展
//         0.7   // 相似度阈值
//       );
//
//       console.log(`📋 [HybridSearch] 数据库返回结果:`, {
//         hasResults: !!result.results,
//         resultsLength: result.results?.length || 0,
//         originalKeywords: result.originalKeywords,
//         expandedKeywords: result.expandedKeywords,
//         totalResults: result.totalResults
//       });
//
//       if (!result.results || result.results.length === 0) {
//         console.warn(`⚠️ [HybridSearch] 智能混合搜索无结果`);
//         console.log(`🔍 [HybridSearch] 详细信息:`, {
//           queryLength: query.length,
//           queryTrimmed: query.trim(),
//           limit: limit,
//           resultStructure: result,
//           originalKeywords: result.originalKeywords,
//           expandedKeywords: result.expandedKeywords
//         });
//         return {
//           images: [],
//           total: 0,
//           loading: false
//         };
//       }
//
//       const images = result.results.map((record: ImageRecord) => {
//         const imageData = this.convertImageRecordToImageData(record);
//         // 使用智能搜索的综合得分
//         if ('finalScore' in record && typeof record.finalScore === 'number') {
//           imageData.similarity = Math.round(record.finalScore * 100);
//         } else if ('score' in record && typeof record.score === 'number') {
//           imageData.similarity = Math.round(record.score * 100);
//         }
//         return imageData;
//       });
//
//       console.log(`Gallery Service: 智能混合搜索完成，返回 ${images.length} 张图片`);
//
//       return {
//         images,
//         total: result.totalResults,
//         loading: false,
//         metadata: {
//           totalMatches: result.totalResults,
//           originalKeywords: result.originalKeywords,
//           expandedKeywords: result.expandedKeywords
//         }
//       };
//
//     } catch (error) {
//       console.error('Gallery Service: 智能混合搜索失败:', error);
//       throw error; // 重新抛出错误，让调用者处理
//     }
//   }
//
//   /**
//    * 使用指定阈值执行向量搜索
//    */
//   async vectorSearchWithThreshold(
//     query: string,
//     threshold: number,
//     limit: number = 50
//   ): Promise<GalleryQueryResult> {
//     try {
//       console.log(`Gallery Service: 执行语义向量搜索 - 查询: "${query}", 阈值: ${threshold}`);
//
//       // 检查数据库中图片总数
//       try {
//         const dbStats = await databaseService.queryImages({ limit: 1 });
//         console.log(`📚 [调试] 数据库状态检查: 总计 ${dbStats.total || 0} 张图片`);
//         if (dbStats.total === 0) {
//           console.warn(`⚠️ [调试] 数据库为空，无法进行搜索`);
//         }
//       } catch (statsError) {
//         console.warn(`⚠️ [调试] 无法获取数据库状态:`, statsError);
//       }
//
//       // 生成查询文本的向量
//       const embeddingResult = await window.electronAPI.ai.generateEmbedding(query);
//
//       if (embeddingResult?.error) {
//         throw new Error(embeddingResult.error);
//       }
//
//       if (!embeddingResult?.embedding) {
//         throw new Error('向量生成失败');
//       }
//       console.log('向量生成成功:', embeddingResult.embedding.length)
//
//       // 使用向量进行相似度搜索
//       const result = await databaseService.searchSimilarImages(
//         embeddingResult.embedding,
//         limit,
//         threshold
//       );
//
//       if (result.error) {
//         throw new Error(result.error);
//       }
//
//       console.log(`🔍 [调试] 原始搜索结果数量: ${result.results.length}`);
//       console.log(`📊 [调试] 原始搜索结果详情:`, result.results.slice(0, 5).map(r => ({
//         id: 'id' in r ? r.id : 'unknown',
//         score: 'score' in r ? r.score : 'no score',
//         hasScore: 'score' in r,
//         scoreType: 'score' in r ? typeof r.score : 'undefined'
//       })));
//       console.log(`🔬 [调试] 第一个结果完整对象:`, result.results[0]);
//
//       // 根据阈值过滤结果
//       const filteredResults = result.results.filter((record) => {
//         // 尝试多种可能的 score 字段位置
//         let score = 0;
//         if ('score' in record && typeof record.score === 'number') {
//           score = record.score;
//         } else if (record.metadata && 'similarityScore' in record.metadata && typeof record.metadata.similarityScore === 'number') {
//           score = record.metadata.similarityScore;
//         } else if ('similarity_score' in record && typeof record.similarity_score === 'number') {
//           score = record.similarity_score;
//         }
//        
//         const passesThreshold = score >= threshold;
//         if (result.results.length <= 10) {
//           console.log(`🎯 [调试] 图片 ${('id' in record ? record.id : 'unknown')}: score=${score}, threshold=${threshold}, passes=${passesThreshold}, 原始对象键:`, Object.keys(record));
//         }
//         return passesThreshold;
//       });
//
//       const images = filteredResults.map((record) => {
//         const imageData = this.convertImageRecordToImageData(record);
//         // 使用真实的向量检索相似度分数
//         let score = 0;
//         if ('score' in record && typeof record.score === 'number') {
//           score = record.score;
//         } else if (record.metadata && 'similarityScore' in record.metadata && typeof record.metadata.similarityScore === 'number') {
//           score = record.metadata.similarityScore;
//         } else if ('similarity_score' in record && typeof record.similarity_score === 'number') {
//           score = record.similarity_score;
//         }
//        
//         if (score > 0) {
//           imageData.similarity = Math.round(score * 100);
//         }
//         return imageData;
//       });
//
//       console.log(`Gallery Service: 语义向量搜索完成，阈值过滤后返回 ${images.length} 张图片`);
//
//       return {
//         images,
//         total: filteredResults.length,
//         loading: false
//       };
//
//     } catch (error) {
//       console.error('Gallery Service: 语义向量搜索失败:', error);
//       throw error; // 重新抛出错误，让调用者处理
//     }
//   }
//
//   /**
//    * 生成相关性原因说明
//    */
//   private generateRelevanceReason(
//     image: ImageData,
//     query: string,
//     method: 'hybrid' | 'vector'
//   ): string {
//     const queryLower = query.toLowerCase();
//     const titleMatch = image.title.toLowerCase().includes(queryLower);
//     const tagMatches = image.tags.some((tag) => tag.toLowerCase().includes(queryLower));
//     const descMatch = image.description.toLowerCase().includes(queryLower);
//
//     if (method === 'hybrid') {
//       const reasons = [];
//       if (titleMatch) reasons.push('标题匹配');
//       if (tagMatches) reasons.push('标签匹配');
//       if (descMatch) reasons.push('描述匹配');
//       if (reasons.length === 0) reasons.push('语义相似');
//
//       return `智能混合: ${reasons.join(', ')}`;
//     } else {
//       return `语义相似度: ${image.similarity}%`;
//     }
//   }
//
//   /**
//    * 智能语义搜索图片（使用 tRPC client）
//    */
//   async searchImages(query: string, limit: number = 50): Promise<GalleryQueryResult> {
//     const startTime = Date.now();
//
//     try {
//       console.log(`Gallery Service: 开始智能搜索 - 查询: "${query}"`);
//      
//       // 使用 tRPC 智能搜索
//       const result = await trpcClient.image.smartSearch.query({
//         query,
//         limit
//       });
//      
//       if (result.error || !result.results || result.results.length === 0) {
//         console.warn('智能搜索无结果，尝试向量搜索');
//         return await this.fallbackSearchWithTRPC(query, limit, startTime);
//       }
//
//       const duration = Date.now() - startTime;
//
//       const images = result.results.map((record: ImageRecord) => {
//         const imageData = this.convertImageRecordToImageData(record);
//         // 使用智能搜索的综合得分
//         if ('finalScore' in record && typeof record.finalScore === 'number') {
//           imageData.similarity = Math.round(record.finalScore * 100);
//         } else if ('score' in record && typeof record.score === 'number') {
//           imageData.similarity = Math.round(record.score * 100);
//         }
//         return imageData;
//       });
//      
//       console.log(`Gallery Service: 智能搜索完成，耗时: ${duration}ms，返回 ${images.length} 张图片`);
//      
//       return {
//         images,
//         total: result.totalResults,
//         loading: false,
//         metadata: {
//           totalMatches: result.totalResults,
//           originalKeywords: [],
//           expandedKeywords: []
//         }
//       };
//      
//     } catch (error) {
//       console.error('Gallery Service: 智能搜索失败:', error);
//       return this.fallbackSearchWithTRPC(query, limit, startTime);
//     }
//   }
//  
//   /**
//    * 备用搜索方法（语义向量搜索，带回退跟踪）
//    */
//   private async fallbackSearch(query: string, limit: number = 50, startTime?: number): Promise<GalleryQueryResult> {
//     const searchStartTime = startTime || Date.now();
//
//     try {
//       console.log(`Gallery Service: 使用语义向量搜索方法 - 查询: "${query}"`);
//      
//       // 首先生成查询文本的向量
//       const embeddingResult = await window.electronAPI?.ai.generateEmbedding(query);
//      
//       if (embeddingResult?.error) {
//         throw new Error(embeddingResult.error);
//       }
//
//       if (!embeddingResult?.embedding) {
//         throw new Error('向量生成失败');
//       }
//
//       // 使用向量进行相似度搜索，获取更多结果以便过滤
//       const result = await databaseService.searchSimilarImages(
//         embeddingResult.embedding, 
//         limit * 2 // 获取更多结果以便过滤
//       );
//      
//       if (result.error) {
//         return {
//           images: [],
//           total: 0,
//           loading: false,
//           error: result.error
//         };
//       }
//
//       const duration = Date.now() - searchStartTime;
//
//       const images = result.results.map((record) => {
//         const imageData = this.convertImageRecordToImageData(record);
//         // 使用真实的向量检索相似度分数，转换为百分比显示
//         if ('score' in record && typeof record.score === 'number') {
//           // 将 0-1 范围的score转换为百分比，并进行适当的映射
//           // Milvus COSINE距离: 1 = 完全相同，0 = 完全不同
//           imageData.similarity = Math.round(record.score * 100);
//         }
//         return imageData;
//       });
//      
//       console.log(`Gallery Service: 语义向量搜索完成，耗时: ${duration}ms，返回 ${images.length} 张图片`);
//      
//       return {
//         images,
//         total: result.results.length,
//         loading: false
//       };
//      
//     } catch (error) {
//       console.error('Gallery Service: 语义向量搜索失败:', error);
//       return {
//         images: [],
//         total: 0,
//         loading: false,
//         error: error instanceof Error ? error.message : String(error)
//       };
//     }
//   }
//
//   /**
//    * 使用 tRPC 的备用搜索方法
//    */
//   private async fallbackSearchWithTRPC(query: string, limit: number = 50, startTime?: number): Promise<GalleryQueryResult> {
//     const searchStartTime = startTime || Date.now();
//
//     try {
//       console.log(`Gallery Service: 使用 tRPC 向量搜索方法 - 查询: "${query}"`);
//      
//       // 首先生成查询文本的向量
//       const embeddingResult = await window.electronAPI?.ai.generateEmbedding(query);
//      
//       if (embeddingResult?.error) {
//         throw new Error(embeddingResult.error);
//       }
//
//       if (!embeddingResult?.embedding) {
//         throw new Error('向量生成失败');
//       }
//
//       // 使用 tRPC 向量搜索
//       const result = await trpcClient.image.vectorSearch.query({
//         embedding: embeddingResult.embedding,
//         threshold: 0.6,
//         limit
//       });
//      
//       if (result.error) {
//         return {
//           images: [],
//           total: 0,
//           loading: false,
//           error: result.error
//         };
//       }
//
//       const duration = Date.now() - searchStartTime;
//
//       const images = result.images.map((record) => {
//         const imageData = this.convertImageRecordToImageData(record);
//         // 使用真实的向量检索相似度分数，转换为百分比显示
//         if ('score' in record && typeof record.score === 'number') {
//           imageData.similarity = Math.round(record.score * 100);
//         }
//         return imageData;
//       });
//      
//       console.log(`Gallery Service: tRPC 向量搜索完成，耗时: ${duration}ms，返回 ${images.length} 张图片`);
//      
//       return {
//         images,
//         total: result.total,
//         loading: false
//       };
//      
//     } catch (error) {
//       console.error('Gallery Service: tRPC 向量搜索失败:', error);
//       return {
//         images: [],
//         total: 0,
//         loading: false,
//         error: error instanceof Error ? error.message : String(error)
//       };
//     }
//   }
//
//   /**
//    * 应用筛选和排序
//    */
//   applyFiltersAndSort(
//     images: ImageData[], 
//     filters: FilterOptions = {}, 
//     sortOption: SortOption = SORT_OPTIONS[0]
//   ): ImageData[] {
//     let filteredImages = [...images]
//
//     // 应用搜索查询筛选
//     if (filters.searchQuery) {
//       const query = filters.searchQuery.toLowerCase()
//       filteredImages = filteredImages.filter((image) => 
//         image.title.toLowerCase().includes(query) ||
//         image.description.toLowerCase().includes(query) ||
//         image.tags.some((tag) => tag.toLowerCase().includes(query))
//       )
//     }
//
//     // 应用标签筛选
//     const tags = filters.tags
//     if (tags && tags.length > 0) {
//       filteredImages = filteredImages.filter((image) =>
//         tags.some((tag) => image.tags.includes(tag))
//       )
//     }
//
//     // 应用日期筛选
//     if (filters.dateRange) {
//       const dateRange = filters.dateRange
//       if (dateRange.start) {
//         filteredImages = filteredImages.filter((image) =>
//           image.uploadTime >= dateRange.start!
//         )
//       }
//       if (dateRange.end) {
//         filteredImages = filteredImages.filter((image) =>
//           image.uploadTime <= dateRange.end!
//         )
//       }
//     }
//
//     // 应用排序
//     return this.sortImages(filteredImages, sortOption)
//   }
//
//   /**
//    * 排序图片
//    */
//   private sortImages(images: ImageData[], sortOption: SortOption): ImageData[] {
//     return [...images].sort((a, b) => {
//       const aValue = a[sortOption.key]
//       const bValue = b[sortOption.key]
//      
//       let comparison = 0
//      
//       if (typeof aValue === 'string' && typeof bValue === 'string') {
//         comparison = aValue.localeCompare(bValue)
//       } else if (typeof aValue === 'number' && typeof bValue === 'number') {
//         comparison = aValue - bValue
//       } else {
//         comparison = String(aValue).localeCompare(String(bValue))
//       }
//      
//       return sortOption.direction === 'desc' ? -comparison : comparison
//     })
//   }
//
//   /**
//    * 转换 ImageRecord 到 ImageData
//    */
//   private convertImageRecordToImageData(record: ImageRecord): ImageData {
//     console.log('转换图片记录:', {
//       id: record.id,
//       imagePath: record.imagePath,
//       tagsCount: record.tags.length,
//       tagsFlatCount: record.tags_flat?.length || 0,
//       hasStructuredMetadata: !!record.structured_metadata,
//       hasMetadata: !!record.metadata
//     });
//    
//     const imageData: ImageData = {
//       id: record.id,
//       url: record.imagePath, // 直接使用原始路径
//       title: record.metadata.filename.replace(/\.[^/.]+$/, ""), // 移除文件扩展名
//       tags: record.tags,
//       description: record.description,
//       uploadTime: record.metadata.uploadTime.split('T')[0], // 只保留日期部分
//       location: '本地存储',
//       camera: '未知设备',
//       colors: this.extractColorsFromTags(record.tags_flat || record.tags), // 优先使用tags_flat
//       aiAnalysis: true, // 已经通过AI分析
//       similarity: 0, // 默认相似度
//       fileSize: this.formatFileSize(record.metadata.filesize),
//       resolution: record.metadata.dimensions,
//       exif: {
//         iso: 0,
//         aperture: 'f/0',
//         shutterSpeed: '0s',
//         focalLength: '0mm'
//       }
//     };
//    
//     // 保存原始数据库记录的引用，供其他方法使用
//     (imageData as ImageData & { record: ImageRecord }).record = record;
//    
//     return imageData;
//   }
//
//   /**
//    * 标准化图片路径，将绝对路径转换为相对路径
//    */
//   private normalizeImagePath(imagePath: string): string {
//     // 如果是Windows绝对路径，提取文件名
//     if (imagePath.includes('\\') && imagePath.includes(':')) {
//       return imagePath.split('\\').pop() || imagePath
//     }
//    
//     // 如果是Unix绝对路径，提取文件名
//     if (imagePath.startsWith('/')) {
//       return imagePath.split('/').pop() || imagePath
//     }
//    
//     // 已经是相对路径，直接返回
//     return imagePath
//   }
//
//   /**
//    * 从标签中提取颜色信息
//    */
//   private extractColorsFromTags(tags: string[]): string[] {
//     const colorKeywords = ['红', '蓝', '绿', '黄', '白', '黑', '紫', '橙', '粉', '灰']
//     const colors = tags.filter((tag) => 
//       colorKeywords.some((color) => tag.includes(color))
//     )
//     return colors.length > 0 ? colors : ['未分析']
//   }
//
//   /**
//    * 格式化文件大小
//    */
//   private formatFileSize(bytes: number): string {
//     if (bytes === 0) return '0 Bytes'
//     const k = 1024
//     const sizes = ['Bytes', 'KB', 'MB', 'GB']
//     const i = Math.floor(Math.log(bytes) / Math.log(k))
//     return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
//   }
//
//   /**
//    * 获取所有唯一标签（带缓存和智能初始化）
//    */
//   async getAllTags(): Promise<string[]> {
//     try {
//       // 检查缓存是否有效
//       const now = Date.now()
//       if (this.tagsCache && (now - this.tagsCacheTime) < this.CACHE_DURATION) {
//         console.log('Gallery Service: 使用缓存的标签数据')
//         return this.tagsCache
//       }
//
//       console.log('Gallery Service: 从数据库获取标签数据')
//       // 优先尝试使用优化的标签查询方法
//       const tagResult = await databaseService.queryAllTags()
//       if (!tagResult.error && tagResult.tags && tagResult.tags.length > 0) {
//         console.log(`Gallery Service: 直接标签查询获取到 ${tagResult.tags.length} 个标签`)
//        
//         // 更新缓存
//         this.tagsCache = tagResult.tags
//         this.tagsCacheTime = now
//        
//         return tagResult.tags
//       }
//      
//       // 如果直接查询失败，则回退到传统方法
//       console.log('Gallery Service: 直接标签查询失败，使用传统方法')
//       const result = await this.getAllImages(200) // 增加到200张图片以获取更多标签
//       if (result.error || !result.images) {
//         return []
//       }
//
//       // 优先使用tags_flat，如果没有则使用传统的tags
//       const allTags = result.images.flatMap(image => {
//         // 尝试从数据库的原始record中获取tags_flat
//         const record = (image as ImageData & { record?: ImageRecord }).record;
//         if (record && record.tags_flat && Array.isArray(record.tags_flat) && record.tags_flat.length > 0) {
//           return record.tags_flat;
//         }
//         // 向后兼容：使用传统的tags
//         return Array.isArray(image.tags) ? image.tags : [];
//       });
//      
//       const uniqueTags = Array.from(new Set(allTags))
//      
//       // 按使用频率排序
//       const sortedTags = uniqueTags.sort((a, b) => {
//         const countA = allTags.filter((tag) => tag === a).length
//         const countB = allTags.filter((tag) => tag === b).length
//         return countB - countA
//       })
//
//       // 更新缓存
//       this.tagsCache = sortedTags
//       this.tagsCacheTime = now
//
//       return sortedTags
//     } catch (error) {
//       console.error('获取标签失败:', error)
//       return []
//     }
//   }
//
//   /**
//    * 清除标签缓存
//    */
//   clearTagsCache(): void {
//     this.tagsCache = null
//     this.tagsCacheTime = 0
//   }
//  
//   /**
//    * 带重试机制的混合搜索
//    */
//   private async hybridSearchOnlyWithRetry(query: string, limit: number = 50, maxRetries: number = 2): Promise<GalleryQueryResult> {
//     let lastError: Error | null = null;
//
//     for (let attempt = 1; attempt <= maxRetries; attempt++) {
//       try {
//         console.log(`Gallery Service: 混合搜索尝试 ${attempt}/${maxRetries}`);
//         return await this.hybridSearchOnly(query, limit);
//       } catch (error) {
//         lastError = error instanceof Error ? error : new Error(String(error));
//         console.warn(`Gallery Service: 混合搜索尝试 ${attempt} 失败:`, lastError.message);
//
//         if (attempt < maxRetries) {
//           // 等待一段时间后重试
//           await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
//         }
//       }
//     }
//
//     throw lastError || new Error('混合搜索重试失败');
//   }
//
//   /**
//    * 带重试机制的向量搜索
//    */
//   private async vectorSearchWithThresholdAndRetry(
//     query: string,
//     threshold: number,
//     limit: number = 50,
//     maxRetries: number = 2
//   ): Promise<GalleryQueryResult> {
//     let lastError: Error | null = null;
//
//     for (let attempt = 1; attempt <= maxRetries; attempt++) {
//       try {
//         console.log(`Gallery Service: 向量搜索尝试 ${attempt}/${maxRetries}`);
//         return await this.vectorSearchWithThreshold(query, threshold, limit);
//       } catch (error) {
//         lastError = error instanceof Error ? error : new Error(String(error));
//         console.warn(`Gallery Service: 向量搜索尝试 ${attempt} 失败:`, lastError.message);
//
//         if (attempt < maxRetries) {
//           // 等待一段时间后重试
//           await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
//         }
//       }
//     }
//
//     throw lastError || new Error('向量搜索重试失败');
//   }
//
//   /**
//    * 从异常创建搜索错误对象
//    */
//   private createSearchErrorFromException(error: unknown, method: SearchMethod): SearchError {
//     const actualError = error instanceof Error ? error : new Error(String(error));
//     const errorType = getErrorTypeFromError(actualError, method);
//     return createSearchError(errorType, method, actualError);
//   }
//
//   /**
//    * 创建回退错误（当主搜索和回退搜索都失败时）
//    */
//   private createFallbackError(originalError: SearchError, fallbackError: unknown, method: SearchMethod): SearchError {
//     const fallbackErrorObj = fallbackError instanceof Error ? fallbackError : new Error(String(fallbackError));
//
//     return {
//       type: 'UNKNOWN_ERROR',
//       message: `智能混合搜索和语义向量搜索都失败了。原始错误: ${originalError.message}，回退错误: ${fallbackErrorObj.message}`,
//       originalError: originalError.originalError,
//       method,
//       suggestions: [
//         '检查网络连接和AI服务状态',
//         '尝试使用更简单的搜索词',
//         '重启应用程序',
//         '联系技术支持'
//       ],
//       retryable: true,
//       fallbackAvailable: false
//     };
//   }
//
//   /**
//    * 创建错误结果对象
//    */
//   private createErrorResult(
//     searchError: SearchError,
//     startTime: number,
//     method: SearchMethod,
//     threshold?: number
//   ): EnhancedGalleryQueryResult {
//     const duration = Date.now() - startTime;
//
//     return {
//       images: [],
//       total: 0,
//       loading: false,
//       error: searchError.message,
//       metadata: {
//         method,
//         actualMethod: 'vector', // 默认值
//         fallbackOccurred: searchError.fallbackAvailable,
//         duration,
//         threshold,
//         totalResults: 0,
//         timestamp: new Date().toISOString()
//       },
//       searchError // 添加详细的错误信息供UI使用
//     };
//   }
//
//   /**
//    * 精简版图片搜索方法 - 不带重试和fallback，专注日志输出用于调试
//    */
//   async searchImagesWithMethodSimple(
//     query: string,
//     method: SearchMethod,
//     threshold?: number,
//     limit: number = 50
//   ): Promise<EnhancedGalleryQueryResult> {
//     const startTime = Date.now();
//    
//     // 详细日志：输入参数
//     console.log(`🚀 [SimpleSearch] 开始搜索 - 开始时间: ${new Date().toISOString()}`);
//     console.log(`📋 [SimpleSearch] 搜索参数:`, {
//       query: query,
//       method: method,
//       threshold: threshold,
//       limit: limit,
//       queryLength: query.length,
//       queryWords: query.split(' ').length
//     });
//
//     // 参数验证
//     if (!query || query.trim().length === 0) {
//       console.error(`❌ [SimpleSearch] 搜索查询为空`);
//       const searchError = createSearchError('INVALID_QUERY', method, undefined, '搜索查询不能为空');
//       return this.createErrorResult(searchError, startTime, method, threshold);
//     }
//
//     // 验证阈值
//     if (threshold !== undefined) {
//       console.log(`🔍 [SimpleSearch] 验证阈值: ${threshold}`);
//       const validation = validateThreshold(threshold);
//       if (!validation.isValid) {
//         console.error(`❌ [SimpleSearch] 阈值验证失败:`, validation);
//         const searchError = createSearchError('INVALID_THRESHOLD', method, undefined, validation.warning);
//         return this.createErrorResult(searchError, startTime, method, threshold);
//       }
//       console.log(`✅ [SimpleSearch] 阈值验证通过`);
//     }
//
//     try {
//       console.log(`🎯 [SimpleSearch] 执行搜索方法: ${method}`);
//      
//       let result: GalleryQueryResult;
//       let actualMethod: 'hybrid' | 'vector';
//
//       switch (method) {
//         case 'auto':
//           console.log(`🤖 [SimpleSearch] 自动选择方法 - 默认使用混合搜索`);
//           try {
//             console.log(`🔄 [SimpleSearch] 尝试混合搜索...`);
//             result = await this.hybridSearchOnly(query, limit);
//             actualMethod = 'hybrid';
//             console.log(`✅ [SimpleSearch] 混合搜索成功`);
//           } catch (error) {
//             console.error(`❌ [SimpleSearch] 混合搜索失败，直接返回错误:`, error);
//             const searchError = this.createSearchErrorFromException(error, 'hybrid');
//             return this.createErrorResult(searchError, startTime, method, threshold);
//           }
//           break;
//
//         case 'hybrid':
//           console.log(`🔀 [SimpleSearch] 执行混合搜索`);
//           try {
//             result = await this.hybridSearchOnly(query, limit);
//             actualMethod = 'hybrid';
//             console.log(`✅ [SimpleSearch] 混合搜索完成`);
//           } catch (error) {
//             console.error(`❌ [SimpleSearch] 混合搜索失败:`, error);
//             const hybridError = this.createSearchErrorFromException(error, 'hybrid');
//             return this.createErrorResult(hybridError, startTime, method, threshold);
//           }
//           break;
//
//         case 'vector':
//           console.log(`🎯 [SimpleSearch] 执行向量搜索`);
//           const actualThreshold = threshold || 0.6;
//           console.log(`📊 [SimpleSearch] 使用阈值: ${actualThreshold}`);
//           try {
//             result = await this.vectorSearchWithThreshold(query, actualThreshold, limit);
//             actualMethod = 'vector';
//             console.log(`✅ [SimpleSearch] 向量搜索完成`);
//           } catch (error) {
//             console.error(`❌ [SimpleSearch] 向量搜索失败:`, error);
//             const vectorError = this.createSearchErrorFromException(error, 'vector');
//             return this.createErrorResult(vectorError, startTime, method, threshold);
//           }
//           break;
//
//         default:
//           console.error(`❌ [SimpleSearch] 不支持的搜索方法: ${method}`);
//           const unknownError = createSearchError('UNKNOWN_ERROR', method, undefined, `不支持的搜索方法: ${method}`);
//           return this.createErrorResult(unknownError, startTime, method, threshold);
//       }
//
//       const duration = Date.now() - startTime;
//
//       // 详细日志：搜索结果
//       console.log(`📈 [SimpleSearch] 搜索结果统计:`, {
//         totalResults: result.total,
//         imagesReturned: result.images.length,
//         hasError: !!result.error,
//         duration: `${duration}ms`
//       });
//
//       if (result.images.length > 0) {
//         // 计算平均相似度
//         const similarities = result.images.map(img => img.similarity || 0).filter(s => s > 0);
//         const averageSimilarity = similarities.length > 0 
//           ? similarities.reduce((sum, s) => sum + s, 0) / similarities.length 
//           : undefined;
//        
//         console.log(`📊 [SimpleSearch] 相似度统计:`, {
//           averageSimilarity: averageSimilarity ? `${averageSimilarity.toFixed(2)}%` : 'N/A',
//           maxSimilarity: `${Math.max(...similarities)}%`,
//           minSimilarity: `${Math.min(...similarities)}%`,
//           validSimilarities: similarities.length
//         });
//
//         // 详细的结果信息
//         console.log(`📝 [SimpleSearch] 前5个结果详情:`);
//         result.images.slice(0, 5).forEach((img, index) => {
//           console.log(`  ${index + 1}. ID: ${img.id}, 标题: "${img.title}", 相似度: ${img.similarity}%, 标签数: ${img.tags.length}`);
//         });
//       } else {
//         console.warn(`⚠️  [SimpleSearch] 无搜索结果`);
//       }
//
//       // 创建搜索元数据
//       const metadata: SearchMetadata = {
//         method,
//         actualMethod,
//         fallbackOccurred: false, // 简化版不使用fallback
//         duration,
//         threshold,
//         totalResults: result.total,
//         averageSimilarity: result.images.length > 0
//           ? result.images.reduce((sum, img) => sum + (img.similarity || 0), 0) / result.images.length
//           : undefined,
//         timestamp: new Date().toISOString()
//       };
//
//       // 转换为增强结果格式
//       const enhancedImages: EnhancedImageData[] = result.images.map((img) => ({
//         ...img,
//         searchMetadata: {
//           matchType: actualMethod,
//           similarity: img.similarity,
//           relevanceReason: this.generateRelevanceReason(img, query, actualMethod)
//         }
//       }));
//
//       console.log(`🎉 [SimpleSearch] 搜索完成 - 方法: ${actualMethod}, 耗时: ${duration}ms, 结果: ${result.images.length}/${result.total}`);
//       console.log(`📋 [SimpleSearch] 元数据:`, metadata);
//       console.log(`🏁 [SimpleSearch] 结束时间: ${new Date().toISOString()}`);
//
//       return {
//         images: enhancedImages,
//         total: result.total,
//         loading: false,
//         error: result.error,
//         metadata
//       };
//
//     } catch (error) {
//       const duration = Date.now() - startTime;
//       console.error(`💥 [SimpleSearch] 搜索过程中发生未捕获错误:`, {
//         error: error,
//         message: error instanceof Error ? error.message : String(error),
//         stack: error instanceof Error ? error.stack : undefined,
//         duration: `${duration}ms`,
//         query: query,
//         method: method
//       });
//      
//       const searchError = this.createSearchErrorFromException(error, method);
//       return this.createErrorResult(searchError, startTime, method, threshold);
//     }
//   }
//
//   /**
//    * 获取结构化标签数据（按分类组织）
//    */
//   async getStructuredTags(): Promise<{
//     theme: { [key: string]: string[] },
//     tags: { [key: string]: string[] },
//     objects: Array<{ name: string; attributes: string[] }>
//   }> {
//     try {
//       const result = await this.getAllImages(200)
//       if (result.error) {
//         return { theme: {}, tags: {}, objects: [] }
//       }
//
//       const structuredTags = {
//         theme: {
//           dominant_colors: [] as string[],
//           scene: [] as string[],
//           mood: [] as string[],
//           time: [] as string[],
//           style: [] as string[]
//         },
//         tags: {
//           objects: [] as string[],
//           actions: [] as string[],
//           clothing: [] as string[],
//           relationships: [] as string[],
//           activity_domain: [] as string[],
//           text_overlay: [] as string[]
//         },
//         objects: [] as Array<{ name: string; attributes: string[] }>
//       }
//
//       // 从所有图片中提取结构化数据
//       result.images.forEach((image) => {
//         const record = (image as ImageData & { record?: ImageRecord }).record
//         if (record && record.structured_metadata) {
//           const structured = record.structured_metadata
//          
//           // 提取theme数据
//           if (structured.theme) {
//             const theme = structured.theme
//             if (theme.dominant_colors) structuredTags.theme.dominant_colors.push(...theme.dominant_colors)
//             if (theme.scene) structuredTags.theme.scene.push(theme.scene)
//             if (theme.mood) structuredTags.theme.mood.push(theme.mood)
//             if (theme.time) structuredTags.theme.time.push(theme.time)
//             if (theme.style) structuredTags.theme.style.push(theme.style)
//           }
//          
//           // 提取tags数据
//           if (structured.tags) {
//             const tags = structured.tags
//             if (tags.objects) structuredTags.tags.objects.push(...tags.objects)
//             if (tags.actions) structuredTags.tags.actions.push(...tags.actions)
//             if (tags.clothing) structuredTags.tags.clothing.push(...tags.clothing)
//             if (tags.relationships) structuredTags.tags.relationships.push(...tags.relationships)
//             if (tags.activity_domain) structuredTags.tags.activity_domain.push(...tags.activity_domain)
//             if (tags.text_overlay) structuredTags.tags.text_overlay.push(...tags.text_overlay)
//           }
//          
//           // 提取objects数据
//           if (structured.objects) {
//             structuredTags.objects.push(...structured.objects)
//           }
//         }
//       })
//
//       // 去重和排序
//       Object.keys(structuredTags.theme).forEach((key) => {
//         const typedKey = key as keyof typeof structuredTags.theme;
//         structuredTags.theme[typedKey] = Array.from(new Set(structuredTags.theme[typedKey]));
//       })
//      
//       Object.keys(structuredTags.tags).forEach((key) => {
//         const typedKey = key as keyof typeof structuredTags.tags;
//         structuredTags.tags[typedKey] = Array.from(new Set(structuredTags.tags[typedKey]));
//       })
//      
//       // 对objects去重（按name）
//       const uniqueObjects = new Map<string, string[]>()
//       structuredTags.objects.forEach((obj) => {
//         if (uniqueObjects.has(obj.name)) {
//           uniqueObjects.get(obj.name)!.push(...obj.attributes)
//         } else {
//           uniqueObjects.set(obj.name, [...obj.attributes])
//         }
//       })
//      
//       structuredTags.objects = Array.from(uniqueObjects.entries()).map(([name, attributes]) => ({
//         name,
//         attributes: Array.from(new Set(attributes))
//       }))
//
//       return structuredTags
//     } catch (error) {
//       console.error('获取结构化标签失败:', error)
//       return { theme: {}, tags: {}, objects: [] }
//     }
//   }
// }