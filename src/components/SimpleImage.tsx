import { AlertCircle } from 'lucide-react'

interface SimpleImageProps {
  imagePath: string
  alt: string
  className?: string
  fallbackSrc?: string
}

export function SimpleImage({ imagePath, alt, className, fallbackSrc }: SimpleImageProps): JSX.Element {
  // 将Windows路径转换为app:// URL格式
  const fileUrl = `app://${imagePath.replace(/\\/g, '/').replace(/^([A-Za-z]):/, '$1')}`

  const handleError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    console.error('❌ [SimpleImage] 图片加载失败:', fileUrl)
    console.error('❌ [SimpleImage] 原始路径:', imagePath)
    if (fallbackSrc) {
      console.log('🔄 [SimpleImage] 使用备用图片:', fallbackSrc)
      e.currentTarget.src = fallbackSrc
    }
  }

  const handleLoad = () => {
    console.log('✅ [SimpleImage] 图片加载成功:', fileUrl)
  }

  return (
    <img 
      src={fileUrl}
      alt={alt}
      className={className}
      onError={handleError}
      onLoad={handleLoad}
    />
  )
}