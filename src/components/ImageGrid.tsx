import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card.tsx'
import { Badge } from '@/components/ui/badge.tsx'
import { Button } from '@/components/ui/button.tsx'
import { SimpleImage } from './SimpleImage.tsx'
import { 
  Image as ImageIcon, 
  Heart, 
  Share2, 
  Download, 
  Eye,
  Calendar,
  MapPin,
  Camera,
  Palette,
  Zap,
  Trash2,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { ImageData } from '../data/mockData'

// 类型定义
interface ImageGridProps {
  images: ImageData[]
  onImageSelect?: (image: ImageData) => void
  onImageDelete?: (image: ImageData) => Promise<void>
  viewMode?: 'grid' | 'list'  // Added viewMode prop
}

export function ImageGrid({ images, onImageSelect, onImageDelete, viewMode = 'grid' }: ImageGridProps): JSX.Element {
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null)
  const [hoveredImage, setHoveredImage] = useState<string | number | null>(null)
  const [deleteConfirm, setDeleteConfirm] = useState<string | number | null>(null)
  const [deleting, setDeleting] = useState<string | number | null>(null)

  const handleImageClick = (image: ImageData): void => {
    setSelectedImage(image)
    onImageSelect?.(image)
  }

  const handleDeleteClick = (e: React.MouseEvent, image: ImageData): void => {
    e.stopPropagation()
    setDeleteConfirm(image.id)
  }

  const handleDeleteConfirm = async (image: ImageData): Promise<void> => {
    if (!onImageDelete) return
    
    setDeleting(image.id)
    setDeleteConfirm(null)
    
    try {
      await onImageDelete(image)
    } catch (error) {
      console.error('删除图片失败:', error)
    } finally {
      setDeleting(null)
    }
  }

  const handleDeleteCancel = (): void => {
    setDeleteConfirm(null)
  }

  const getTagColor = (tag: string): string => {
    const colors: Record<string, string> = {
      '自然': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      '城市': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      '人物': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      '动物': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
      '建筑': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
      '食物': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      'AI分析中': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      '新上传': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200'
    }
    return colors[tag] || 'bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-200'
  }

  return (
    <div className="space-y-6">
      {/* 图像网格 */}
      <div className={`
        ${viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4' 
          : 'space-y-4'
        }
      `}>
        {images.map((image) => (
          <Card 
            key={image.id} 
            className={`
              group overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-1
              ${selectedImage?.id === image.id ? 'ring-2 ring-blue-500 shadow-lg' : ''}
            `}
            onClick={() => handleImageClick(image)}
            onMouseEnter={() => setHoveredImage(image.id)}
            onMouseLeave={() => setHoveredImage(null)}
          >
            <div className="relative aspect-square bg-slate-200 dark:bg-slate-700 overflow-hidden">
              {/* 图像显示 */}
              {image.url.includes(':\\') || image.url.startsWith('/') ? (
                // 本地图片路径 - 使用简化组件
                <SimpleImage
                  imagePath={image.url}
                  alt={image.title}
                  className="w-full h-full object-cover"
                  fallbackSrc="/api/placeholder/400/300"
                />
              ) : (
                // 网络图片或占位符
                <SimpleImage
                  imagePath={image.url}
                  alt={image.title}
                  className="w-full h-full object-cover"
                  fallbackSrc="/api/placeholder/400/300"
                />
              )}
              
              {/* 备用占位符 */}
              <div className="hidden w-full h-full flex items-center justify-center">
                <ImageIcon className="h-12 w-12 text-slate-400" />
              </div>
              
              {/* 悬停覆盖层 */}
              <div className={`
                absolute inset-0 bg-black/50 flex items-center justify-center space-x-2 transition-opacity duration-300
                ${hoveredImage === image.id ? 'opacity-100' : 'opacity-0'}
              `}>
                <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                  <Eye className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                  <Heart className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                  <Share2 className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                  <Download className="h-4 w-4" />
                </Button>
                {onImageDelete && (
                  <Button 
                    size="sm" 
                    variant="destructive" 
                    className="h-8 w-8 p-0"
                    onClick={(e) => handleDeleteClick(e, image)}
                    disabled={deleting === image.id}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {/* AI分析状态指示器 */}
              {image.aiAnalysis && (
                <div className="absolute top-2 right-2">
                  <Badge className="bg-green-500 text-white">
                    <Zap className="h-3 w-3 mr-1" />
                    AI已分析
                  </Badge>
                </div>
              )}
            </div>

            <CardContent className="p-4">
              <div className="space-y-3">
                {/* 标题和描述 */}
                <div>
                  <h4 className="font-semibold text-sm line-clamp-1">{image.title}</h4>
                  <p className="text-xs text-slate-600 dark:text-slate-400 line-clamp-2 mt-1">
                    {image.description}
                  </p>
                </div>

                {/* 标签 */}
                <div className="flex flex-wrap gap-1">
                  {image.tags?.slice(0, 3).map((tag, index) => (
                    <Badge 
                      key={index} 
                      variant="secondary" 
                      className={`text-xs ${getTagColor(tag)}`}
                    >
                      {tag}
                    </Badge>
                  ))}
                  {image.tags?.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{image.tags.length - 3}
                    </Badge>
                  )}
                </div>

                {/* 元数据 */}
                <div className="flex items-center justify-between text-xs text-slate-500">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-3 w-3" />
                    <span>{image.uploadTime}</span>
                  </div>
                  {image.location && (
                    <div className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3" />
                      <span>{image.location}</span>
                    </div>
                  )}
                </div>

                {/* 相似度分数（搜索结果时显示） */}
                {image.similarity && image.similarity > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-slate-600 dark:text-slate-400">相似度</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 h-1 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-gradient-to-r from-blue-500 to-green-500 transition-all duration-500"
                          style={{ width: `${image.similarity}%` }}
                        />
                      </div>
                      <span className="text-xs font-medium">{image.similarity}%</span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 空状态 */}
      {images.length === 0 && (
        <div className="text-center py-12">
          <div className="p-4 bg-slate-100 dark:bg-slate-800 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <ImageIcon className="h-8 w-8 text-slate-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2">暂无图像</h3>
          <p className="text-slate-600 dark:text-slate-400">
            上传一些图片开始使用智能相册功能
          </p>
        </div>
      )}

      {/* 选中图像详情模态框 */}
      {selectedImage && (
        <div className="absolute inset-0 bg-black/40 backdrop-blur-sm flex items-center justify-center z-50 p-6">
          <div className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-md rounded-2xl shadow-2xl border border-slate-200/50 dark:border-slate-700/50 max-w-6xl w-full max-h-[90vh] overflow-hidden flex">
            {/* 左侧：图片展示 */}
            <div className="flex-1 flex items-center justify-center bg-slate-50/50 dark:bg-slate-800/50 p-8 min-w-0">
              <div className="w-full max-w-3xl">
                <div className="bg-slate-200 dark:bg-slate-700 rounded-xl flex items-center justify-center overflow-hidden shadow-lg">
                  <SimpleImage
                    imagePath={selectedImage.url}
                    alt={selectedImage.title}
                    className="w-full h-auto max-h-[70vh] object-contain"
                    fallbackSrc="/api/placeholder/400/300"
                  />
                  
                  {/* 备用占位符 */}
                  <div className="hidden w-full h-64 flex items-center justify-center">
                    <ImageIcon className="h-16 w-16 text-slate-400" />
                  </div>
                </div>
              </div>
            </div>

            {/* 右侧：详细信息 */}
            <div className="w-96 bg-white/60 dark:bg-slate-900/60 backdrop-blur-sm border-l border-slate-200/50 dark:border-slate-700/50 flex flex-col shrink-0">
              {/* 标题栏 */}
              <div className="flex items-center justify-between p-6 border-b border-slate-200/50 dark:border-slate-700/50">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 rounded-full bg-[#007aff]/10 flex items-center justify-center">
                    <ImageIcon className="h-3 w-3 text-[#007aff]" />
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white">图片详情</h3>
                </div>
                <Button 
                  variant="ghost" 
                  size="icon"
                  onClick={() => setSelectedImage(null)}
                  className="h-8 w-8 rounded-full hover:bg-slate-100 dark:hover:bg-slate-800"
                >
                  <span className="text-lg">×</span>
                </Button>
              </div>

              {/* 内容区域 */}
              <div className="flex-1 p-6 overflow-y-auto space-y-6">
                {/* 基本信息 */}
                <div className="space-y-3">
                  <h4 className="text-base font-medium text-slate-900 dark:text-white">{selectedImage.title}</h4>
                  <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                    {selectedImage.description}
                  </p>
                </div>

                {/* 标签 */}
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-900 dark:text-white">标签</h5>
                  <div className="flex flex-wrap gap-2">
                    {selectedImage.tags?.map((tag, index) => (
                      <Badge 
                        key={index} 
                        variant="secondary"
                        className="text-xs bg-[#007aff]/10 text-[#007aff] border-[#007aff]/20 hover:bg-[#007aff]/20 transition-colors"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* 基本属性 */}
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-900 dark:text-white">基本信息</h5>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-slate-400" />
                        <span className="text-slate-600 dark:text-slate-400">上传时间</span>
                      </div>
                      <span className="text-slate-900 dark:text-white">{selectedImage.uploadTime}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <ImageIcon className="h-4 w-4 text-slate-400" />
                        <span className="text-slate-600 dark:text-slate-400">文件大小</span>
                      </div>
                      <span className="text-slate-900 dark:text-white">{selectedImage.fileSize}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <ImageIcon className="h-4 w-4 text-slate-400" />
                        <span className="text-slate-600 dark:text-slate-400">分辨率</span>
                      </div>
                      <span className="text-slate-900 dark:text-white">{selectedImage.resolution}</span>
                    </div>
                    
                    {selectedImage.camera && (
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-2">
                          <Camera className="h-4 w-4 text-slate-400" />
                          <span className="text-slate-600 dark:text-slate-400">设备</span>
                        </div>
                        <span className="text-slate-900 dark:text-white">{selectedImage.camera}</span>
                      </div>
                    )}
                    
                    {selectedImage.location && (
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-4 w-4 text-slate-400" />
                          <span className="text-slate-600 dark:text-slate-400">位置</span>
                        </div>
                        <span className="text-slate-900 dark:text-white">{selectedImage.location}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* EXIF 信息 */}
                {selectedImage.exif && (
                  <div className="space-y-3">
                    <h5 className="text-sm font-medium text-slate-900 dark:text-white">拍摄参数</h5>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="bg-slate-50/50 dark:bg-slate-800/50 rounded-lg p-3">
                        <div className="text-xs text-slate-600 dark:text-slate-400">ISO</div>
                        <div className="text-sm font-medium text-slate-900 dark:text-white">{selectedImage.exif.iso}</div>
                      </div>
                      <div className="bg-slate-50/50 dark:bg-slate-800/50 rounded-lg p-3">
                        <div className="text-xs text-slate-600 dark:text-slate-400">光圈</div>
                        <div className="text-sm font-medium text-slate-900 dark:text-white">{selectedImage.exif.aperture}</div>
                      </div>
                      <div className="bg-slate-50/50 dark:bg-slate-800/50 rounded-lg p-3">
                        <div className="text-xs text-slate-600 dark:text-slate-400">快门</div>
                        <div className="text-sm font-medium text-slate-900 dark:text-white">{selectedImage.exif.shutterSpeed}</div>
                      </div>
                      <div className="bg-slate-50/50 dark:bg-slate-800/50 rounded-lg p-3">
                        <div className="text-xs text-slate-600 dark:text-slate-400">焦距</div>
                        <div className="text-sm font-medium text-slate-900 dark:text-white">{selectedImage.exif.focalLength}</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 色彩信息 */}
                {selectedImage.colors && selectedImage.colors.length > 0 && (
                  <div className="space-y-3">
                    <h5 className="text-sm font-medium text-slate-900 dark:text-white">主色调</h5>
                    <div className="flex flex-wrap gap-2">
                      {selectedImage.colors.map((color, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className="w-4 h-4 rounded-full bg-slate-300 dark:bg-slate-600 border border-slate-200 dark:border-slate-700"></div>
                          <span className="text-sm text-slate-600 dark:text-slate-400">{color}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* AI 分析状态 */}
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-900 dark:text-white">AI 分析</h5>
                  <div className="flex items-center space-x-2">
                    {selectedImage.aiAnalysis ? (
                      <>
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <span className="text-sm text-green-600 dark:text-green-400">已完成分析</span>
                      </>
                    ) : (
                      <>
                        <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                        <span className="text-sm text-yellow-600 dark:text-yellow-400">待分析</span>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="p-6 border-t border-slate-200/50 dark:border-slate-700/50">
                <div className="flex space-x-3">
                  <Button size="sm" className="flex-1 bg-[#007aff] hover:bg-[#0056cc] text-white">
                    <Share2 className="h-4 w-4 mr-2" />
                    分享
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm">
                    <Download className="h-4 w-4 mr-2" />
                    下载
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认对话框 */}
      {deleteConfirm && (
        <div className="absolute inset-0 bg-black/40 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 dark:border-slate-700/50 max-w-md w-full">
            <div className="p-6">
              <div className="space-y-4">
                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mx-auto mb-4">
                    <Trash2 className="h-6 w-6 text-red-600 dark:text-red-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white">确认删除</h3>
                  <p className="text-sm text-slate-600 dark:text-slate-400 mt-2">
                    您确定要删除这张图片吗？此操作无法撤销。
                  </p>
                </div>
                
                <div className="flex justify-center space-x-3">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleDeleteCancel}
                    className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm"
                  >
                    取消
                  </Button>
                  <Button 
                    variant="destructive" 
                    size="sm"
                    onClick={() => {
                      const imageToDelete = images.find((img: ImageData) => img.id === deleteConfirm)
                      if (imageToDelete) {
                        handleDeleteConfirm(imageToDelete)
                      }
                    }}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    删除
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 