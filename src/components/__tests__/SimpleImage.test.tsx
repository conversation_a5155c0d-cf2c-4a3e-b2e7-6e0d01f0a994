import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { SimpleImage } from '../SimpleImage'

describe('SimpleImage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Clear console mocks
    vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('renders image with correct app:// URL conversion for Windows path', () => {
    const windowsPath = 'C:\\Users\\<USER>\\Pictures\\image.jpg'
    
    render(
      <SimpleImage
        imagePath={windowsPath}
        alt="Test Image"
        className="test-class"
      />
    )
    
    const img = screen.getByAltText('Test Image')
    expect(img).toHaveAttribute('src', 'app://C/Users/<USER>/Pictures/image.jpg')
    expect(img).toHaveClass('test-class')
  })

  it('converts Unix-style paths correctly', () => {
    const unixPath = '/home/<USER>/pictures/image.jpg'
    
    render(
      <SimpleImage
        imagePath={unixPath}
        alt="Unix Image"
      />
    )
    
    const img = screen.getByAltText('Unix Image')
    expect(img).toHaveAttribute('src', 'app:///home/<USER>/pictures/image.jpg')
  })

  it('logs path conversion information', () => {
    const consoleSpy = vi.spyOn(console, 'log')
    const testPath = 'D:\\Photos\\vacation.png'
    
    render(
      <SimpleImage
        imagePath={testPath}
        alt="Vacation Photo"
      />
    )
    
    expect(consoleSpy).toHaveBeenCalledWith('🔍 [SimpleImage] 原始路径:', testPath)
    expect(consoleSpy).toHaveBeenCalledWith('🔗 [SimpleImage] 转换后URL:', 'app://D/Photos/vacation.png')
  })

  it('handles successful image load', () => {
    const consoleSpy = vi.spyOn(console, 'log')
    const testPath = 'C:\\test.jpg'
    
    render(
      <SimpleImage
        imagePath={testPath}
        alt="Test"
      />
    )
    
    const img = screen.getByAltText('Test')
    fireEvent.load(img)
    
    expect(consoleSpy).toHaveBeenCalledWith('✅ [SimpleImage] 图片加载成功:', 'app://C/test.jpg')
  })

  it('handles image load error without fallback', () => {
    const consoleErrorSpy = vi.spyOn(console, 'error')
    const testPath = 'C:\\nonexistent.jpg'
    
    render(
      <SimpleImage
        imagePath={testPath}
        alt="Nonexistent"
      />
    )
    
    const img = screen.getByAltText('Nonexistent')
    fireEvent.error(img)
    
    expect(consoleErrorSpy).toHaveBeenCalledWith('❌ [SimpleImage] 图片加载失败:', 'app://C/nonexistent.jpg')
    expect(consoleErrorSpy).toHaveBeenCalledWith('❌ [SimpleImage] 原始路径:', testPath)
  })

  it('handles image load error with fallback', () => {
    const consoleLogSpy = vi.spyOn(console, 'log')
    const consoleErrorSpy = vi.spyOn(console, 'error')
    const testPath = 'C:\\broken.jpg'
    const fallbackSrc = '/placeholder.png'
    
    render(
      <SimpleImage
        imagePath={testPath}
        alt="Broken Image"
        fallbackSrc={fallbackSrc}
      />
    )
    
    const img = screen.getByAltText('Broken Image')
    fireEvent.error(img)
    
    expect(consoleErrorSpy).toHaveBeenCalledWith('❌ [SimpleImage] 图片加载失败:', 'app://C/broken.jpg')
    expect(consoleLogSpy).toHaveBeenCalledWith('🔄 [SimpleImage] 使用备用图片:', fallbackSrc)
    expect(img).toHaveAttribute('src', fallbackSrc)
  })

  it('handles paths with special characters', () => {
    const specialPath = 'C:\\用户\\照片\\我的图片 (1).jpg'
    
    render(
      <SimpleImage
        imagePath={specialPath}
        alt="Special Characters"
      />
    )
    
    const img = screen.getByAltText('Special Characters')
    expect(img).toHaveAttribute('src', 'app://C/用户/照片/我的图片 (1).jpg')
  })

  it('handles mixed path separators', () => {
    const mixedPath = 'C:/Users\\test/Pictures\\image.jpg'
    
    render(
      <SimpleImage
        imagePath={mixedPath}
        alt="Mixed Separators"
      />
    )
    
    const img = screen.getByAltText('Mixed Separators')
    expect(img).toHaveAttribute('src', 'app://C/Users/<USER>/Pictures/image.jpg')
  })

  it('handles relative paths', () => {
    const relativePath = '.\\images\\thumbnail.jpg'
    
    render(
      <SimpleImage
        imagePath={relativePath}
        alt="Relative Path"
      />
    )
    
    const img = screen.getByAltText('Relative Path')
    expect(img).toHaveAttribute('src', 'app://./images/thumbnail.jpg')
  })

  it('applies custom className', () => {
    render(
      <SimpleImage
        imagePath="C:\\test.jpg"
        alt="Styled Image"
        className="custom-class another-class"
      />
    )
    
    const img = screen.getByAltText('Styled Image')
    expect(img).toHaveClass('custom-class', 'another-class')
  })

  it('works without optional props', () => {
    render(
      <SimpleImage
        imagePath="C:\\minimal.jpg"
        alt="Minimal"
      />
    )
    
    const img = screen.getByAltText('Minimal')
    expect(img).toBeInTheDocument()
    // The path conversion adds an extra slash for root paths
    expect(img).toHaveAttribute('src', 'app://C//minimal.jpg')
  })

  it('preserves drive letters correctly', () => {
    const testCases = [
      { input: 'A:\\file.jpg', expected: 'app://A/file.jpg' },
      { input: 'Z:\\documents\\photo.png', expected: 'app://Z/documents/photo.png' },
      { input: 'c:\\lowercase\\image.gif', expected: 'app://c/lowercase/image.gif' }
    ]
    
    testCases.forEach(({ input, expected }) => {
      const { unmount } = render(
        <SimpleImage
          imagePath={input}
          alt={`Test ${input}`}
        />
      )
      
      const img = screen.getByAltText(`Test ${input}`)
      expect(img).toHaveAttribute('src', expected)
      
      unmount()
    })
  })
})