// 类型定义
export interface ImageData {
  id: string | number
  url: string
  title: string
  tags: string[]
  description: string
  uploadTime: string
  location: string
  camera: string
  colors: string[]
  aiAnalysis: boolean
  similarity: number
  fileSize: string
  resolution: string
  exif: {
    iso: number
    aperture: string
    shutterSpeed: string
    focalLength: string
  }
}

// ReAct步骤接口
export interface ReActStep {
  id: string;
  type: 'thought' | 'action' | 'observation' | 'final';
  content: string;
  timestamp: Date;
  status: 'pending' | 'active' | 'completed' | 'error';
  toolName?: string;
  toolParams?: Record<string, unknown>;
  toolResult?: any;
  executionTime?: number;
}

export interface ChatMessage {
  id: number
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  image?: {
    url: string
    description?: string
  }
  isStreaming?: boolean // 新增：标识是否为流式消息
  isError?: boolean // 新增：标识是否为错误消息
  reactSteps?: ReActStep[] // 新增：ReAct步骤
  currentReActStep?: ReActStep // 新增：当前正在执行的ReAct步骤
  toolResults?: Array<{
    toolCallId: string
    result: {
      success: boolean
      data?: any
      error?: string
      metadata?: {
        executionTime?: number
        toolName?: string
        parameters?: any
      }
    }
  }>
}

export interface StatsData {
  totalImages: number
  analyzedImages: number
  storageUsed: string
  processingSpeed: string
  totalTags: number
  uniqueLocations: number
}

// 模拟图像数据
export const mockImages: ImageData[] = [
  {
    id: 1,
    url: '/api/placeholder/400/300',
    title: '蓝天白云风景',
    tags: ['自然', '天空', '云朵', '风景', '户外'],
    description: '晴朗的天空中飘着白云，阳光明媚的好天气，远处可见青山绿水',
    uploadTime: '2024-01-15',
    location: '北京·奥林匹克公园',
    camera: 'iPhone 15 Pro',
    colors: ['蓝色', '白色', '绿色'],
    aiAnalysis: true,
    similarity: 95,
    fileSize: '2.3MB',
    resolution: '4032x3024',
    exif: {
      iso: 100,
      aperture: 'f/2.8',
      shutterSpeed: '1/250s',
      focalLength: '24mm'
    }
  },
  {
    id: 2,
    url: '/api/placeholder/400/300',
    title: '城市夜景',
    tags: ['城市', '夜晚', '灯光', '建筑', '摩天大楼'],
    description: '繁华的城市夜景，灯火通明的摩天大楼倒映在江面上',
    uploadTime: '2024-01-14',
    location: '上海·陆家嘴',
    camera: 'Canon EOS R5',
    colors: ['蓝色', '黄色', '黑色', '橙色'],
    aiAnalysis: true,
    similarity: 88,
    fileSize: '4.1MB',
    resolution: '6720x4480',
    exif: {
      iso: 800,
      aperture: 'f/4.0',
      shutterSpeed: '1/60s',
      focalLength: '85mm'
    }
  },
  {
    id: 3,
    url: '/api/placeholder/400/300',
    title: '咖啡与书本',
    tags: ['咖啡', '书籍', '生活', '静物', '温馨'],
    description: '桌上的咖啡杯和打开的书本，温馨的阅读时光，旁边还有一支钢笔',
    uploadTime: '2024-01-13',
    location: '家中书房',
    camera: 'Sony A7R IV',
    colors: ['棕色', '白色', '黄色', '黑色'],
    aiAnalysis: true,
    similarity: 92,
    fileSize: '3.2MB',
    resolution: '7952x5304',
    exif: {
      iso: 400,
      aperture: 'f/5.6',
      shutterSpeed: '1/125s',
      focalLength: '50mm'
    }
  },
  {
    id: 4,
    url: '/api/placeholder/400/300',
    title: '春日樱花',
    tags: ['自然', '花朵', '春天', '粉色', '樱花'],
    description: '盛开的樱花树，粉色花瓣随风飘落，春意盎然的美好时光',
    uploadTime: '2024-03-20',
    location: '日本·东京',
    camera: 'Fujifilm X-T5',
    colors: ['粉色', '绿色', '白色'],
    aiAnalysis: true,
    similarity: 90,
    fileSize: '2.8MB',
    resolution: '6240x4160',
    exif: {
      iso: 200,
      aperture: 'f/2.8',
      shutterSpeed: '1/500s',
      focalLength: '35mm'
    }
  },
  {
    id: 5,
    url: '/api/placeholder/400/300',
    title: '海边日落',
    tags: ['自然', '海洋', '日落', '橙色', '浪漫'],
    description: '海边的美丽日落，橙红色的夕阳倒映在波光粼粼的海面上',
    uploadTime: '2024-02-10',
    location: '三亚·天涯海角',
    camera: 'Nikon Z9',
    colors: ['橙色', '红色', '蓝色', '黄色'],
    aiAnalysis: true,
    similarity: 87,
    fileSize: '3.5MB',
    resolution: '8256x5504',
    exif: {
      iso: 100,
      aperture: 'f/8.0',
      shutterSpeed: '1/250s',
      focalLength: '70mm'
    }
  },
  {
    id: 6,
    url: '/api/placeholder/400/300',
    title: '雨后彩虹',
    tags: ['自然', '彩虹', '雨后', '天空', '奇观'],
    description: '雨后天空中出现的美丽彩虹，七色光带横跨天际',
    uploadTime: '2024-02-28',
    location: '杭州·西湖',
    camera: 'iPhone 15 Pro Max',
    colors: ['红色', '橙色', '黄色', '绿色', '蓝色', '紫色'],
    aiAnalysis: true,
    similarity: 93,
    fileSize: '2.1MB',
    resolution: '4032x3024',
    exif: {
      iso: 125,
      aperture: 'f/2.8',
      shutterSpeed: '1/120s',
      focalLength: '24mm'
    }
  },
  {
    id: 7,
    url: '/api/placeholder/400/300',
    title: '古建筑群',
    tags: ['建筑', '古典', '文化', '历史', '传统'],
    description: '古色古香的传统建筑群，红墙黄瓦，展现深厚的历史文化底蕴',
    uploadTime: '2024-01-20',
    location: '北京·故宫',
    camera: 'Canon EOS R6',
    colors: ['红色', '黄色', '蓝色', '白色'],
    aiAnalysis: true,
    similarity: 89,
    fileSize: '3.8MB',
    resolution: '5472x3648',
    exif: {
      iso: 200,
      aperture: 'f/5.6',
      shutterSpeed: '1/200s',
      focalLength: '24mm'
    }
  },
  {
    id: 8,
    url: '/api/placeholder/400/300',
    title: '美食拼盘',
    tags: ['美食', '料理', '色彩', '精致', '诱人'],
    description: '精美的美食拼盘，色彩丰富，摆盘精致，令人垂涎欲滴',
    uploadTime: '2024-01-25',
    location: '上海·米其林餐厅',
    camera: 'Sony A7 III',
    colors: ['红色', '绿色', '黄色', '白色', '棕色'],
    aiAnalysis: true,
    similarity: 91,
    fileSize: '2.9MB',
    resolution: '6000x4000',
    exif: {
      iso: 400,
      aperture: 'f/4.0',
      shutterSpeed: '1/60s',
      focalLength: '85mm'
    }
  }
]

// 搜索建议
export const searchSuggestions: string[] = [
  '去年拍的蓝天白云',
  '有咖啡的照片',
  '城市夜景',
  '人物肖像',
  '自然风景',
  '美食照片',
  '建筑摄影',
  '动物照片',
  '生活记录',
  '黑白照片',
  '日落时分',
  '雨天的照片',
  '春天的花朵',
  '海边风景',
  '古建筑',
  '街头摄影',
  '微距摄影',
  '星空银河',
  '雪景照片',
  '节日庆典'
]

// 热门标签
export const popularTags: string[] = [
  '蓝天白云', '城市夜景', '美食照片', '人物肖像', 
  '自然风景', '建筑摄影', '动物照片', '生活记录',
  '春天花朵', '海边日落', '古典建筑', '街头摄影',
  '微距特写', '星空银河', '雪景冬日', '节日庆典'
]

// AI分析模拟响应
export const aiResponses: Record<string, string[]> = {
  '这是什么': [
    '根据图像分析，这是一张风景照片，包含了蓝天、白云等自然元素。图片色彩丰富，构图优美。',
    '这是一张城市夜景照片，展现了现代都市的繁华景象，灯光璀璨，建筑宏伟。',
    '这是一张美食照片，展示了精心制作的料理，色彩搭配和谐，摆盘精致。',
    '这是一张人物肖像照片，光线柔和，表情自然，构图专业。'
  ],
  '拍摄地点': [
    '根据图片的GPS信息和视觉特征分析，这张照片可能拍摄于城市公园或开阔的户外场所。',
    '从建筑风格和环境特征来看，这张照片应该拍摄于现代化的都市中心区域。',
    '根据背景环境和光线条件分析，这张照片可能拍摄于室内或有遮蔽的环境中。',
    '从自然环境特征来看，这张照片拍摄于户外自然景区或公园。'
  ],
  '相似图片': [
    '我找到了3张与此图片相似的照片，它们都包含相似的色彩搭配和构图元素。',
    '在您的相册中有5张类似风格的照片，主要相似点在于拍摄主题和色调。',
    '发现了4张相关图片，它们在拍摄时间、地点或主题上有相似性。',
    '找到了6张相似的照片，它们在视觉风格和内容主题上有共同点。'
  ],
  '图片详情': [
    '这张图片拍摄于2024年1月15日，使用iPhone 15 Pro拍摄，分辨率为4032x3024，文件大小约2.5MB。',
    '照片信息：拍摄时间2024年1月14日，设备Canon EOS R5，ISO 800，光圈f/4.0，快门1/60s。',
    '图片详情：Sony A7R IV拍摄，分辨率7952x5304，文件大小3.2MB，焦距50mm。',
    '拍摄参数：Fujifilm X-T5，ISO 200，光圈f/2.8，快门1/500s，焦距35mm。'
  ]
}

// 聊天历史模拟
export const initialChatHistory: ChatMessage[] = [
  {
    id: 1,
    type: 'assistant',
    content: '您好！我是您的本地多模态智能助理。您可以向我询问关于图片的任何问题，比如图片内容、拍摄时间、相似图片等。我会基于AI视觉分析为您提供详细解答。',
    timestamp: new Date(Date.now() - 300000) // 5分钟前
  },
  {
    id: 2,
    type: 'user',
    content: '这张蓝天白云的照片是什么时候拍的？',
    timestamp: new Date(Date.now() - 240000) // 4分钟前
  },
  {
    id: 3,
    type: 'assistant',
    content: '根据图片的EXIF信息，这张蓝天白云的照片拍摄于2024年1月15日下午2:30。照片显示了晴朗的天空和蓬松的白云，是一个天气很好的日子。拍摄设备是iPhone 15 Pro，使用了24mm焦距，光圈f/2.8。',
    timestamp: new Date(Date.now() - 230000) // 约4分钟前
  }
]

// 统计数据
export const statsData: StatsData = {
  totalImages: 8,
  analyzedImages: 8,
  storageUsed: '2.3GB',
  processingSpeed: '1.2s/张',
  totalTags: 45,
  uniqueLocations: 6
} 