import { publicProcedure, router } from '../trpc/router'
import { ImageService } from '../services/ImageService'
import { TagService } from '../services/TagService'
import { AIAnalysisService } from '../services/AIAnalysisService'
import { SearchMethod } from '@shared/types/search'

export class SearchController {
  constructor(
    private imageService: ImageService,
    private tagService: TagService,
    private aiAnalysisService: AIAnalysisService
  ) {}

  getRouter() {
    return router({
      // 统一搜索接口 - 支持多种搜索方法
      searchImages: publicProcedure
        .input((val: unknown) => {
          return val as {
            query: string
            method: SearchMethod
            threshold?: number
            limit?: number
          }
        })
        .query(async ({ input: params }) => {
          const startTime = Date.now()
          
          try {
            let result
            let actualMethod: 'hybrid' | 'vector' = 'hybrid'
            let fallbackOccurred = false

            // 根据搜索方法执行不同的搜索策略
            switch (params.method) {
              case 'auto':
                // 自动选择：优先混合搜索，失败时回退到向量搜索
                try {
                  result = await this.imageService.smartSearch(params.query)
                  actualMethod = 'hybrid'
                } catch (error) {
                  console.warn('混合搜索失败，回退到向量搜索:', error)
                  const embedding = await this.aiAnalysisService.generateEmbedding(params.query)
                  result = await this.imageService.vectorSearchImages(
                    embedding, 
                    params.threshold || 0.6, 
                    params.limit || 50
                  )
                  actualMethod = 'vector'
                  fallbackOccurred = true
                }
                break

              case 'hybrid':
                // 智能混合搜索
                result = await this.imageService.smartSearch(params.query)
                actualMethod = 'hybrid'
                break

              case 'vector':
                // 向量搜索
                const embedding = await this.aiAnalysisService.generateEmbedding(params.query)
                result = await this.imageService.vectorSearchImages(
                  embedding, 
                  params.threshold || 0.6, 
                  params.limit || 50
                )
                actualMethod = 'vector'
                break

              default:
                throw new Error(`不支持的搜索方法: ${params.method}`)
            }

            const duration = Date.now() - startTime

            // 计算搜索质量指标
            const averageSimilarity = result.results.length > 0 
              ? result.results.reduce((sum, img) => sum + (img.similarity || 0), 0) / result.results.length 
              : 0

            // 构建搜索元数据
            const metadata = {
              method: params.method,
              actualMethod,
              fallbackOccurred,
              duration,
              threshold: params.threshold,
              totalResults: result.total,
              averageSimilarity,
              timestamp: new Date().toISOString()
            }

            // 转换数据格式以适配前端
            const enhancedImages = result.results.map(img => ({
              id: img.id,
              url: `file://${img.filePath}`,
              title: img.fileName || img.filePath.split('/').pop() || 'Unknown',
              tags: img.tags ? JSON.parse(img.tags) : [],
              description: img.description || '',
              uploadTime: new Date(img.createdAt).toISOString(),
              location: '', // 可以从metadata中提取
              camera: '', // 可以从metadata中提取
              colors: [], // 可以从AI分析中提取
              aiAnalysis: !!img.description,
              similarity: img.similarity || 0,
              fileSize: '', // 可以从metadata中提取
              resolution: '', // 可以从metadata中提取
              exif: {
                iso: 0,
                aperture: '',
                shutterSpeed: '',
                focalLength: ''
              },
              searchMetadata: {
                matchType: actualMethod as 'keyword' | 'vector' | 'hybrid',
                similarity: img.similarity,
                relevanceReason: `通过${actualMethod === 'hybrid' ? '智能混合' : '语义向量'}搜索匹配`
              }
            }))

            return {
              success: true,
              images: enhancedImages,
              total: result.total,
              loading: false,
              metadata
            }

          } catch (error) {
            const duration = Date.now() - startTime
            
            return {
              success: false,
              images: [],
              total: 0,
              loading: false,
              error: error instanceof Error ? error.message : '搜索失败',
              metadata: {
                method: params.method,
                actualMethod: 'hybrid',
                fallbackOccurred: false,
                duration,
                threshold: params.threshold,
                totalResults: 0,
                timestamp: new Date().toISOString()
              }
            }
          }
        }),

      // 获取热门标签（用于搜索建议）
      getPopularTags: publicProcedure
        .input((val: unknown) => {
          return val as {
            limit?: number
            minFrequency?: number
          } | undefined
        })
        .query(async ({ input: params }) => {
          try {
            const tags = await this.tagService.getPopularTags(
              params?.limit || 10,
              params?.minFrequency || 1
            )
            
            // 转换为简单的字符串数组
            const tagTexts = tags.map(tag => tag.tagText)
            
            return {
              success: true,
              tags: tagTexts
            }
          } catch (error) {
            return {
              success: false,
              tags: [],
              error: error instanceof Error ? error.message : '获取热门标签失败'
            }
          }
        }),

      // 搜索建议（基于查询文本）
      getSearchSuggestions: publicProcedure
        .input((val: unknown) => {
          return val as {
            query: string
            limit?: number
          }
        })
        .query(async ({ input: params }) => {
          try {
            const suggestions = await this.tagService.searchTags(
              params.query,
              params.limit || 5
            )
            
            const suggestionTexts = suggestions.map(tag => tag.tagText)
            
            return {
              success: true,
              suggestions: suggestionTexts
            }
          } catch (error) {
            return {
              success: false,
              suggestions: [],
              error: error instanceof Error ? error.message : '获取搜索建议失败'
            }
          }
        }),

      // 解析搜索查询（提取关键词和意图）
      parseSearchQuery: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .mutation(async ({ input: query }) => {
          try {
            // 这里可以使用AI服务来解析查询意图
            // 暂时使用简单的关键词提取
            const keywords = query.trim().split(/\s+/).filter(word => word.length > 1)
            
            return {
              success: true,
              originalQuery: query,
              keywords,
              suggestedMethod: keywords.length > 3 ? 'hybrid' : 'vector',
              confidence: 0.8
            }
          } catch (error) {
            return {
              success: false,
              originalQuery: query,
              keywords: [],
              error: error instanceof Error ? error.message : '查询解析失败'
            }
          }
        })
    })
  }
}
