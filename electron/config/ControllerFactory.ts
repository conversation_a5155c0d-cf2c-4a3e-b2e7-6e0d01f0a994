import { config } from 'dotenv'
import { ImageDAO } from '../dao/interfaces/ImageDAO'
import { TagDAO } from '../dao/interfaces/TagDAO'
import { LibraryDAO } from '../dao/interfaces/LibraryDAO'
import { SqliteVecImageDAO } from '../dao/sqlite-vec/SqliteVecImageDAO'
import { SqliteVecTagDAO } from '../dao/sqlite-vec/SqliteVecTagDAO'
import { SQLiteLibraryDAO } from '../dao/sqlite/SQLiteLibraryDAO'
import { ImageService } from '../services/ImageService'
import { TagService } from '../services/TagService'
import { LibraryService } from '../services/LibraryService'
import { AIAnalysisService } from '../services/AIAnalysisService'
import { DatabaseService } from '../services/DatabaseService'
import { TaskManager } from '../services/TaskManager'
import { OpenAIService, OpenAIConfig } from '../ai/OpenAIService'
import { ImageController } from '../controllers/ImageController'
import { TagController } from '../controllers/TagController'
import { LibraryController } from '../controllers/LibraryController'
import { AIController } from '../controllers/AIController'
import { DatabaseController } from '../controllers/DatabaseController'

// 加载环境变量
config()

/**
 * 数据源配置和控制器初始化
 * 按照 KISS 原则，直接配置所有服务依赖
 */
export class ControllerFactory {
  private static instance: ControllerFactory | null = null
  
  // DAOs
  private imageDAO: ImageDAO | null = null
  private tagDAO: TagDAO | null = null
  private libraryDAO: LibraryDAO | null = null
  
  // Services
  private imageService: ImageService | null = null
  private tagService: TagService | null = null
  private libraryService: LibraryService | null = null
  private aiAnalysisService: AIAnalysisService | null = null
  private databaseService: DatabaseService | null = null
  private taskManager: TaskManager | null = null
  
  // Controllers
  private imageController: ImageController | null = null
  private tagController: TagController | null = null
  private libraryController: LibraryController | null = null
  private aiController: AIController | null = null
  private databaseController: DatabaseController | null = null

  private constructor() {}

  static getInstance(): ControllerFactory {
    if (!ControllerFactory.instance) {
      ControllerFactory.instance = new ControllerFactory()
    }
    return ControllerFactory.instance
  }

  /**
   * 初始化所有服务和控制器
   */
  async initialize(): Promise<void> {
    console.log('🏗️ 初始化控制器工厂...')
    
    // 1. 初始化DAOs
    await this.initializeDAOs()
    
    // 2. 初始化AI服务
    await this.initializeAIService()
    
    // 3. 初始化业务服务
    await this.initializeServices()
    
    // 4. 初始化控制器
    await this.initializeControllers()
    
    console.log('✅ 控制器工厂初始化完成')
  }

  /**
   * 初始化数据访问层
   */
  private async initializeDAOs(): Promise<void> {
    try {
      console.log('🔧 初始化数据访问层...')
      
      // 数据库路径配置
      const sqliteVecDbPath = process.env.SQLITE_VEC_DB_PATH || './data/sqlite-vec.db'
      const sqliteConfigDbPath = process.env.SQLITE_CONFIG_DB_PATH || './data/library_config.db'
      
      // 初始化 DAOs（按数据源分离）
      this.imageDAO = new SqliteVecImageDAO(sqliteVecDbPath)  // 向量数据库
      this.tagDAO = new SqliteVecTagDAO(sqliteVecDbPath)      // 向量数据库  
      this.libraryDAO = new SQLiteLibraryDAO(sqliteConfigDbPath) // 普通 SQLite
      
      console.log('✅ 数据访问层初始化完成')
    } catch (error) {
      console.error('❌ 数据访问层初始化失败:', error)
      throw error
    }
  }

  /**
   * 初始化AI服务
   */
  private async initializeAIService(): Promise<void> {
    try {
      console.log('🔧 初始化AI服务...')
      
      // AI服务配置
      const aiConfig: OpenAIConfig = {
        vlBaseURL: process.env.AI_VL_BASE_URL || "",
        vlApiKey: process.env.AI_VL_API_KEY || "",
        vlModel: process.env.AI_VL_MODEL || "",
        chatBaseURL: process.env.AI_CHAT_BASE_URL || "",
        chatApiKey: process.env.AI_CHAT_API_KEY || "",
        chatModel: process.env.AI_CHAT_MODEL || "",
        embeddingModel: process.env.AI_EMBEDDING_MODEL || "",
        embeddingBaseURL: process.env.AI_EMBEDDING_BASE_URL,
        embeddingApiKey: process.env.AI_EMBEDDING_KEY,
        timeout: 90000,
        maxRetries: 3
      }

      const openAIService = new OpenAIService(aiConfig)
      this.aiAnalysisService = new AIAnalysisService(openAIService)
      
      console.log('✅ AI服务初始化完成')
    } catch (error) {
      console.error('❌ AI服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 初始化业务服务层
   */
  private async initializeServices(): Promise<void> {
    try {
      console.log('🔧 初始化业务服务层...')

      if (!this.imageDAO || !this.tagDAO || !this.libraryDAO || !this.aiAnalysisService) {
        throw new Error('DAOs or AI service not initialized')
      }

      // 初始化 TaskManager
      this.taskManager = new TaskManager({
        intervalMs: 1000,
        maxConcurrent: 1,
        autoStart: true
      })

      // 按业务域初始化服务
      this.imageService = new ImageService(
        this.imageDAO,
        this.tagDAO,
        this.aiAnalysisService
      )

      this.tagService = new TagService(
        this.tagDAO,
        this.aiAnalysisService
      )

      this.libraryService = new LibraryService(
        this.libraryDAO,
        this.taskManager,
        this.imageService
      )

      this.databaseService = new DatabaseService(
        this.imageDAO,
        this.libraryDAO,
        this.tagDAO
      )

      console.log('✅ 业务服务层初始化完成')
    } catch (error) {
      console.error('❌ 业务服务层初始化失败:', error)
      throw error
    }
  }

  /**
   * 初始化控制器层
   */
  private async initializeControllers(): Promise<void> {
    try {
      console.log('🔧 初始化控制器层...')
      
      if (!this.imageService || !this.tagService || !this.libraryService || !this.aiAnalysisService || !this.databaseService) {
        throw new Error('Services not initialized')
      }
      
      // 按业务域初始化控制器
      this.imageController = new ImageController(this.imageService)
      this.tagController = new TagController(this.tagService)
      this.libraryController = new LibraryController(this.libraryService)
      this.aiController = new AIController(this.aiAnalysisService)
      this.databaseController = new DatabaseController(this.databaseService)
      
      console.log('✅ 控制器层初始化完成')
    } catch (error) {
      console.error('❌ 控制器层初始化失败:', error)
      throw error
    }
  }

  // ============= 获取器方法 =============

  getImageController(): ImageController {
    if (!this.imageController) {
      throw new Error('ImageController not initialized')
    }
    return this.imageController
  }

  getTagController(): TagController {
    if (!this.tagController) {
      throw new Error('TagController not initialized')
    }
    return this.tagController
  }

  getLibraryController(): LibraryController {
    if (!this.libraryController) {
      throw new Error('LibraryController not initialized')
    }
    return this.libraryController
  }

  getAIController(): AIController {
    if (!this.aiController) {
      throw new Error('AIController not initialized')
    }
    return this.aiController
  }

  getDatabaseController(): DatabaseController {
    if (!this.databaseController) {
      throw new Error('DatabaseController not initialized')
    }
    return this.databaseController
  }

  // ============= 清理方法 =============

  /**
   * 关闭所有服务和连接
   */
  async cleanup(): Promise<void> {
    console.log('🔄 关闭所有服务...')
    
    // 这里可以添加清理逻辑，比如关闭数据库连接
    // vectorDb.close()
    // configDb.close()
    
    // 清理 TaskManager
    if (this.taskManager) {
      this.taskManager.destroy()
    }

    // 重置所有实例
    this.imageDAO = null
    this.tagDAO = null
    this.libraryDAO = null
    this.imageService = null
    this.tagService = null
    this.libraryService = null
    this.aiAnalysisService = null
    this.databaseService = null
    this.taskManager = null
    this.imageController = null
    this.tagController = null
    this.libraryController = null
    this.aiController = null
    this.databaseController = null
    
    console.log('✅ 所有服务已关闭')
  }

  /**
   * 重置单例实例
   */
  static reset(): void {
    ControllerFactory.instance = null
  }
}